package cn.teleinfo.ds.auth;

import com.pig4cloud.pig.common.feign.annotation.EnablePigFeignClients;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * <AUTHOR>
 * @date 2018年06月21日 认证授权中心
 */
@EnablePigFeignClients
@EnableDiscoveryClient
@SpringBootApplication
public class DsAuthApplication {

	public static void main(String[] args) {
		SpringApplication.run(DsAuthApplication.class, args);
	}

}
