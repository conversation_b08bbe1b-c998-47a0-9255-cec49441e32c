server:
  port: 9999

spring:
  application:
    name: @artifactId@
  cloud:
    nacos:
      username: nacos
      password: 47Ac5H78@YyNw
      discovery:
        server-addr: ${NACOS_HOST:*************}:${NACOS_PORT:8848}
        watch:
          enabled: true
        watch-delay: 1000
        namespace: ${NACOS_NAMESPACE:public}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: ${spring.cloud.nacos.discovery.namespace}
  config:
    import:
      - optional:nacos:<EMAIL>@.yml
      - optional:nacos:${spring.application.name}-@profiles.active@.yml