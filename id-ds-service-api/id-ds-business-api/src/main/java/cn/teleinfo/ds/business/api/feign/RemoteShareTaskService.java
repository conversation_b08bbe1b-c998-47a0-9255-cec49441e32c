package cn.teleinfo.ds.business.api.feign;

import com.pig4cloud.pig.common.core.constant.SecurityConstants;
import com.pig4cloud.pig.common.core.constant.ServiceNameConstants;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.common.feign.annotation.NoToken;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;


@FeignClient(contextId = "remoteShareTaskService", value = ServiceNameConstants.BUSINESS_SERVICE)
public interface RemoteShareTaskService {

    @NoToken
    @PostMapping("/shared-tasks/execute")
    R execute(@RequestParam("id") Long id, @RequestHeader(SecurityConstants.FROM) String from);

}
