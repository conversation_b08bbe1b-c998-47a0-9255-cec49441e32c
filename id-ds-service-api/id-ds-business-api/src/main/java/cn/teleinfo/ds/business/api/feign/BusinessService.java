package cn.teleinfo.ds.business.api.feign;


import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.common.feign.annotation.NoToken;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import static com.pig4cloud.pig.common.core.constant.ServiceNameConstants.BUSINESS_SERVICE;

@FeignClient(value = BUSINESS_SERVICE)
public interface BusinessService {

	@NoToken
	@GetMapping(value = "/integrated-applications")
	R integratedApps();

	@NoToken
	@GetMapping(value = "/integrated-data-channels")
	R integratedChannels();

	@NoToken
	@GetMapping(value = "/integrated-handles")
	R integratedHandles();

	@NoToken
	@GetMapping(value = "/share-channels/auto-create")
	R autoCreateShareChannel();

}
