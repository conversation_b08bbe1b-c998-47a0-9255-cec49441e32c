package cn.teleinfo.ds.quartz.api.dto;

import lombok.Data;

@Data
public class JobSaveReqDTO {

	/**
	 * 任务名称
	 */
	private String name;

	/**
	 * 任务状态
	 */
	private Integer status;

	/**
	 * 处理器的名字
	 */
	private String handlerName;

	/**
	 * 处理器的参数
	 */
	private String handlerParam;

	/**
	 * CRON 表达式
	 */
	private String cronExpression;

	/**
	 * 重试次数
	 */
	private Integer retryCount;

	/**
	 * 重试间隔
	 */
	private Integer retryInterval;

	/**
	 * 监控超时时间
	 */
	private Integer monitorTimeout;
}
