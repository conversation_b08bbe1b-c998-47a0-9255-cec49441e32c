package cn.teleinfo.ds.quartz.api.feign;

import cn.teleinfo.ds.quartz.api.dto.JobSaveReqDTO;
import com.pig4cloud.pig.common.core.constant.ServiceNameConstants;
import com.pig4cloud.pig.common.core.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(contextId = "remoteJobService", value = ServiceNameConstants.QUARTZ_SERVICE)
public interface RemoteJobService {

	@PostMapping("/job/create")
	R<Long> createJob(JobSaveReqDTO job);
}
