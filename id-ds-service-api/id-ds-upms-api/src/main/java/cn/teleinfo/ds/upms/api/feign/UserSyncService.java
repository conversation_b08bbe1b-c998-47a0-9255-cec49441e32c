package cn.teleinfo.ds.upms.api.feign;

import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.common.feign.annotation.NoToken;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import static com.pig4cloud.pig.common.core.constant.ServiceNameConstants.UPMS_SERVICE;

@FeignClient(name = UPMS_SERVICE)
public interface UserSyncService {

	@NoToken
	@GetMapping(value = "/user/sync")
	R sync();

}
