{"groups": [{"name": "security.xss", "type": "cn.teleinfo.ds.common.xss.config.XssProperties", "sourceType": "cn.teleinfo.ds.common.xss.config.XssProperties"}], "properties": [{"name": "security.xss.enable-escape", "type": "java.lang.Bo<PERSON>an", "description": "[clear 专用] 使用转义，默认关闭", "sourceType": "cn.teleinfo.ds.common.xss.config.XssProperties", "defaultValue": false}, {"name": "security.xss.enabled", "type": "java.lang.Bo<PERSON>an", "description": "开启xss", "sourceType": "cn.teleinfo.ds.common.xss.config.XssProperties", "defaultValue": true}, {"name": "security.xss.mode", "type": "cn.teleinfo.ds.common.xss.config.XssProperties$Mode", "description": "模式：clear 清理（默认），escape 转义", "sourceType": "cn.teleinfo.ds.common.xss.config.XssProperties"}, {"name": "security.xss.path-exclude-patterns", "type": "java.util.List<java.lang.String>", "description": "放行的路由，默认为空", "sourceType": "cn.teleinfo.ds.common.xss.config.XssProperties"}, {"name": "security.xss.path-patterns", "type": "java.util.List<java.lang.String>", "description": "拦截的路由，默认为空", "sourceType": "cn.teleinfo.ds.common.xss.config.XssProperties"}, {"name": "security.xss.pretty-print", "type": "java.lang.Bo<PERSON>an", "description": "[clear 专用] pretty<PERSON>rint，默认关闭： 保留换行", "sourceType": "cn.teleinfo.ds.common.xss.config.XssProperties", "defaultValue": false}, {"name": "security.xss.trim-text", "type": "java.lang.Bo<PERSON>an", "description": "全局：对文件进行首尾 trim", "sourceType": "cn.teleinfo.ds.common.xss.config.XssProperties", "defaultValue": true}], "hints": []}