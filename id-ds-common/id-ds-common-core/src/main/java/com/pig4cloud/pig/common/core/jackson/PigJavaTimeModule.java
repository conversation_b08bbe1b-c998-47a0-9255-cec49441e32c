package com.pig4cloud.pig.common.core.jackson;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.PackageVersion;
import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;

import java.io.IOException;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * java 8 时间默认序列化
 *
 * <AUTHOR>
 * <AUTHOR>
 */

public class PigJavaTimeModule extends SimpleModule {

	public PigJavaTimeModule() {
		super(PackageVersion.VERSION);

		// ======================= 时间序列化规则 ===============================
		// yyyy-MM-dd HH:mm:ss
		this.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DatePattern.NORM_DATETIME_FORMATTER));
		// yyyy-MM-dd
		this.addSerializer(LocalDate.class, new LocalDateSerializer(DateTimeFormatter.ISO_LOCAL_DATE));
		// HH:mm:ss
		this.addSerializer(LocalTime.class, new LocalTimeSerializer(DateTimeFormatter.ISO_LOCAL_TIME));
		// Instant 类型序列化
		this.addSerializer(Instant.class, InstantSerializer.INSTANCE);
		// Duration 类型序列化
		this.addSerializer(Duration.class, DurationSerializer.INSTANCE);

		// ======================= 时间反序列化规则 ==============================
		// 支持多种格式的LocalDateTime反序列化
		this.addDeserializer(LocalDateTime.class, new FlexibleLocalDateTimeDeserializer());
		// yyyy-MM-dd
		this.addDeserializer(LocalDate.class, new LocalDateDeserializer(DateTimeFormatter.ISO_LOCAL_DATE));
		// HH:mm:ss
		this.addDeserializer(LocalTime.class, new LocalTimeDeserializer(DateTimeFormatter.ISO_LOCAL_TIME));
		// Instant 反序列化
		this.addDeserializer(Instant.class, InstantDeserializer.INSTANT);
		// Duration 反序列化
		this.addDeserializer(Duration.class, DurationDeserializer.INSTANCE);
	}

	/**
	 * 灵活的LocalDateTime反序列化器，支持多种日期时间格式
	 */
	public static class FlexibleLocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {

		private static final DateTimeFormatter[] FORMATTERS = {
			DatePattern.NORM_DATETIME_FORMATTER,  // yyyy-MM-dd HH:mm:ss
			DateTimeFormatter.ISO_LOCAL_DATE_TIME, // yyyy-MM-ddTHH:mm:ss
			DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS"), // yyyy-MM-ddTHH:mm:ss.SSS
			DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS"), // yyyy-MM-ddTHH:mm:ss.SSSSSS
			DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSSSSS") // yyyy-MM-ddTHH:mm:ss.SSSSSSSSS
		};

		@Override
		public LocalDateTime deserialize(JsonParser parser, DeserializationContext context) throws IOException {
			String dateTimeString = parser.getText();

			if (dateTimeString == null || dateTimeString.trim().isEmpty()) {
				return null;
			}

			// 尝试使用不同的格式解析
			for (DateTimeFormatter formatter : FORMATTERS) {
				try {
					return LocalDateTime.parse(dateTimeString, formatter);
				} catch (DateTimeParseException e) {
					// 继续尝试下一个格式
				}
			}

			// 如果所有格式都失败，抛出异常
			throw new IOException("无法解析日期时间字符串: " + dateTimeString +
				"，支持的格式包括: yyyy-MM-dd HH:mm:ss, yyyy-MM-ddTHH:mm:ss, yyyy-MM-ddTHH:mm:ss.SSS 等");
		}
	}
}
