package com.pig4cloud.pig.common.core.util;


import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.Join;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.select.SelectItem;
import net.sf.jsqlparser.util.TablesNamesFinder;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class SqlUtils {

	/**
	 * 基础属性 转换sql
	 *
	 * @param sql
	 * @return
	 */
	public static String baseToSql(String sql) {
		if (sql == null || sql.isEmpty()) {
			return sql;
		}
		// 预处理SQL，将多个空格和换行替换为单个空格
		sql = sql.replaceAll("\\s+", " ");

		// 支持多种tid参数格式：:tid, #{tid}, ${tid}
		String tidParam = "(:\\s*tid|#\\{\\s*tid\\s*\\}|\\$\\{\\s*tid\\s*\\})";

		// 先尝试处理所有条件都是tid的情况
		Pattern wherePattern = Pattern.compile(
				"(\\bwhere\\b.*?)($|\\bgroup\\s+by\\b|\\bhaving\\b|\\border\\s+by\\b|\\blimit\\b)",
				Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
		Matcher whereMatcher = wherePattern.matcher(sql);

		if (whereMatcher.find()) {
			String whereClause = whereMatcher.group(1);

			// 提取where子句中的条件部分
			Pattern conditionsPattern = Pattern.compile("\\bwhere\\b\\s+(.*?)$", Pattern.CASE_INSENSITIVE);
			Matcher conditionsMatcher = conditionsPattern.matcher(whereClause);

			if (conditionsMatcher.find()) {
				String conditions = conditionsMatcher.group(1).trim();

				// 使用(?i)使正则表达式对大小写不敏感
				String[] conditionArray = conditions.split("(?i)\\s+and\\s+");

				boolean allTidConditions = true;
				for (String condition : conditionArray) {
					// 检查是否是tid条件
					if (!Pattern.compile("\\w+(?:\\s*\\.\\s*\\w+)?\\s*=\\s*" + tidParam,
							Pattern.CASE_INSENSITIVE).matcher(condition).matches()) {
						allTidConditions = false;
						break;
					}
				}

				// 如果所有条件都是tid条件，则移除整个WHERE子句
				if (allTidConditions) {
					// 使用(?i)使正则表达式对大小写不敏感
					return sql.replaceAll("(?i)" + Pattern.quote(whereClause), " ").trim();
				}
			}
		}

		// 匹配 "and column=:tid" 或 "column=:tid and" 或单独的 "column=:tid"，考虑空格变化
		Pattern andTidPattern = Pattern.compile("(?i)\\s+and\\s+\\w+(?:\\s*\\.\\s*\\w+)?\\s*=\\s*" + tidParam + "\\b");
		Pattern tidAndPattern = Pattern.compile("(?i)\\b\\w+(?:\\s*\\.\\s*\\w+)?\\s*=\\s*" + tidParam + "\\s+and\\s+");
		Pattern onlyTidPattern = Pattern
				.compile("(?i)\\bwhere\\s+\\w+(?:\\s*\\.\\s*\\w+)?\\s*=\\s*" + tidParam + "\\b");

		// 先处理 "and column=:tid" 的情况
		Matcher andTidMatcher = andTidPattern.matcher(sql);
		sql = andTidMatcher.replaceAll("");

		// 处理 "column=:tid and" 的情况
		Matcher tidAndMatcher = tidAndPattern.matcher(sql);
		sql = tidAndMatcher.replaceAll(" ");

		// 处理单独的 "where column=:tid" 的情况
		Matcher onlyTidMatcher = onlyTidPattern.matcher(sql);
		sql = onlyTidMatcher.replaceAll("where");

		// 处理可能出现的 "where " 后面没有条件的情况
		sql = sql.replaceAll("(?i)\\bwhere\\s+$", "");

		// 处理可能出现的 "where and" 的情况（当tid条件被移除后可能出现）
		sql = sql.replaceAll("(?i)\\bwhere\\s+and\\s+", "where ");

		return sql.trim();
	}

	/**
	 * 扩展属性 转换sql
	 *
	 * @param sql
	 * @return
	 */
	public static String extendToSql(String sql) {
		if (sql == null || sql.isEmpty()) {
			return sql;
		}

		// 预处理SQL，将多个空格和换行替换为单个空格
		sql = sql.replaceAll("\\s+", " ");

		// 支持多种tid参数格式：:tid, #{tid}, ${tid}
		String tidParam = "(:\\s*tid|#\\{\\s*tid\\s*\\}|\\$\\{\\s*tid\\s*\\})";

		// 查找WHERE子句中的所有TID条件
		Pattern tidConditionPattern = Pattern.compile(
				"(?i)(\\s+and\\s+|\\bwhere\\s+)(\\w+(?:\\s*\\.\\s*\\w+)?)\\s*=\\s*" + tidParam,
				Pattern.CASE_INSENSITIVE);
		Matcher tidConditionMatcher = tidConditionPattern.matcher(sql);

		// 存储找到的所有TID字段
		Set<String> tidColumns = new HashSet<>();

		while (tidConditionMatcher.find()) {
			// 获取TID字段名并添加到集合中
			tidColumns.add(tidConditionMatcher.group(2));
		}

		// 如果找到了TID字段，处理
		if (!tidColumns.isEmpty()) {
			// 移除WHERE子句中的所有TID条件
			sql = baseToSql(sql);

			// 在SELECT子句中添加TID字段
			Pattern selectPattern = Pattern.compile(
					"(?i)\\bselect\\b\\s+(.*?)\\s+\\bfrom\\b",
					Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
			Matcher selectMatcher = selectPattern.matcher(sql);

			if (selectMatcher.find()) {
				String selectClause = selectMatcher.group(1);
				StringBuilder newSelectAdditions = new StringBuilder();

				// 对每个TID字段进行处理
				for (String tidColumn : tidColumns) {
					// 检查SELECT子句中是否已经包含了TID字段
					boolean tidAlreadyInSelect = Pattern.compile(
							"(?i)\\b" + Pattern.quote(tidColumn) + "\\b",
							Pattern.CASE_INSENSITIVE).matcher(selectClause).find();

					if (!tidAlreadyInSelect) {
						// 添加TID字段到新的SELECT部分
						newSelectAdditions.append(", ").append(tidColumn);
					}
				}

				// 如果有新的字段需要添加到SELECT子句
				if (newSelectAdditions.length() > 0) {
					String newSelectClause = selectClause + newSelectAdditions.toString();
					sql = sql.replace(selectClause, newSelectClause);
				}
			}

			// 检查是否有GROUP BY子句
			Pattern groupByPattern = Pattern.compile(
					"(?i)\\bgroup\\s+by\\b\\s+(.*?)(?=\\s+having\\b|\\s+order\\b|\\s+limit\\b|$)",
					Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
			Matcher groupByMatcher = groupByPattern.matcher(sql);

			if (groupByMatcher.find()) {
				String groupByClause = groupByMatcher.group(1).trim();
				StringBuilder newGroupByAdditions = new StringBuilder();

				// 对每个TID字段进行处理
				for (String tidColumn : tidColumns) {
					// 检查GROUP BY子句中是否已经包含了TID字段
					boolean tidAlreadyInGroupBy = Pattern.compile(
							"(?i)\\b" + Pattern.quote(tidColumn) + "\\b",
							Pattern.CASE_INSENSITIVE).matcher(groupByClause).find();

					if (!tidAlreadyInGroupBy) {
						// 添加TID字段到新的GROUP BY部分
						newGroupByAdditions.append(", ").append(tidColumn);
					}
				}

				// 如果有新的字段需要添加到GROUP BY子句
				if (!newGroupByAdditions.isEmpty()) {
					String newGroupByClause = groupByClause + newGroupByAdditions;
					// 使用正则表达式替换整个GROUP BY子句
					sql = sql.replaceFirst("(?i)\\bgroup\\s+by\\b\\s+" + Pattern.quote(groupByClause),
							"group by " + newGroupByClause);
				}
			}
		}

		return sql.trim();
	}

	public static Set<String> findTableName(String sql) throws JSQLParserException {
		return TablesNamesFinder.findTables(sql);
	}

	public static boolean validateSQL(String sql) {
		try {
			Statement statement = CCJSqlParserUtil.parse(sql);
			return true;
		} catch (JSQLParserException e) {
			return false;
		}
	}

	/**
	 * 提取查询 SQL 中的字段
	 *
	 * @param sql sql
	 * @return 查询字段
	 */
	public static List<String> extractSelectSqlField(String sql) {
		Select select = null;
		try {
			select = (Select) CCJSqlParserUtil.parse(sql);
		} catch (JSQLParserException e) {
			log.error("sql 解析异常 sql={}", sql, e);
			return null;
		}
		PlainSelect plainSelect = select.getPlainSelect();

		List<SelectItem<?>> selectItems = plainSelect.getSelectItems();
		if (selectItems == null || selectItems.isEmpty()) {
			return null;
		}

		List<String> fields = new ArrayList<>();

		for (SelectItem<?> selectItem : selectItems) {
			if (StrUtil.isEmpty(selectItem.getAliasName())) {
				fields.add(selectItem.getExpression().toString());
			} else {
				fields.add(selectItem.getAliasName());
			}
		}

		return fields;
	}

	public static void main(String[] args) {
		String sql = """
				select * from a left join (select * from b  where b.a =1 and b.c = 2 group by  b.b) b on a.a = b.b where a.a =1
				""";
		try {
			Set<String> tableName = findTableName(sql);
			System.out.println(tableName.toString());
		} catch (JSQLParserException e) {
			System.out.println("失败");
		}
		boolean b = validateSQL(sql);
		System.out.println(b);

	}

}
