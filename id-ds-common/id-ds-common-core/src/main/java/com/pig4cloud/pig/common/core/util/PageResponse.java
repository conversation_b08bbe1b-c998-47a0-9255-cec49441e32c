package com.pig4cloud.pig.common.core.util;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class PageResponse<T> {
	/**
	 * 记录
	 */
	private List<T> records;

	/**
	 * 总数
	 */
	private Long total;

	/**
	 * 页大小
	 */
	private Long size;

	/**
	 * 当前页
	 */
	private Long current;

	/**
	 * 总页数
	 */
	private Long pages;
}
