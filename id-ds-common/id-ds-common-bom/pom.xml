<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.teleinfo</groupId>
    <artifactId>id-ds-common-bom</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <description>id-data-share cloud parent</description>

    <properties>
        <!-- 项目版本号 -->
        <revision>3.8.3</revision>
        <spring-boot.version>3.4.5</spring-boot.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <git.commit.plugin>9.0.1</git.commit.plugin>
        <fastjson.version>1.2.83_noneautotype</fastjson.version>
        <springdoc.version>2.8.6</springdoc.version>
        <swagger.core.version>2.2.29</swagger.core.version>
        <mybatis-plus.version>3.5.12</mybatis-plus.version>
        <mysql.version>9.2.0</mysql.version>
        <dynamic-ds.version>4.3.1</dynamic-ds.version>
        <seata.version>1.7.0</seata.version>
        <excel.version>3.4.1</excel.version>
        <asm.version>7.1</asm.version>
        <sms.version>3.0.0</sms.version>
        <jaxb.version>2.3.5</jaxb.version>
        <shardingsphere.version>5.4.1</shardingsphere.version>
        <hutool.version>5.8.38</hutool.version>
        <sentinel.version>1.8.4</sentinel.version>
        <common.io.version>2.18.0</common.io.version>
        <spring.checkstyle.plugin>0.0.43</spring.checkstyle.plugin>
        <flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>
		<huaweicloud-sdk-version>3.1.148</huaweicloud-sdk-version>
    </properties>

    <!-- 定义全局jar版本,模块使用需要再次引入但不用写版本号-->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.teleinfo</groupId>
                <artifactId>id-ds-common-core</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.teleinfo</groupId>
                <artifactId>id-ds-common-datasource</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.teleinfo</groupId>
                <artifactId>id-ds-common-log</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.teleinfo</groupId>
                <artifactId>id-ds-common-mybatis</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.teleinfo</groupId>
                <artifactId>id-ds-common-oss</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.teleinfo</groupId>
                <artifactId>id-ds-common-security</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.teleinfo</groupId>
                <artifactId>id-ds-common-feign</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.teleinfo</groupId>
                <artifactId>id-ds-common-swagger</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.teleinfo</groupId>
                <artifactId>id-ds-common-xss</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.teleinfo</groupId>
                <artifactId>id-ds-common-excel</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.teleinfo</groupId>
                <artifactId>id-ds-upms-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.teleinfo</groupId>
                <artifactId>id-ds-business-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <!--springdoc -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webflux-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations-jakarta</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>
            <!--fastjson 版本-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!-- excel 导入导出 -->
            <dependency>
                <groupId>com.pig4cloud.excel</groupId>
                <artifactId>excel-spring-boot-starter</artifactId>
                <version>${excel.version}</version>
            </dependency>
            <!-- commons.io 覆盖easyexcel-->
            <dependency>
                <artifactId>commons-io</artifactId>
                <groupId>commons-io</groupId>
                <version>${common.io.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>shardingsphere-jdbc-core</artifactId>
                <version>${shardingsphere.version}</version>
            </dependency>
            <!-- 多数据源依赖 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                <version>${dynamic-ds.version}</version>
            </dependency>
            <!--  阿里云短信下发 -->
            <dependency>
                <groupId>io.springboot.sms</groupId>
                <artifactId>aliyun-sms-spring-boot-starter</artifactId>
                <version>${sms.version}</version>
            </dependency>
            <!--orm 相关-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-bom</artifactId>
                <version>${mybatis-plus.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--hutool bom 工具类-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--web 模块-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
                <exclusions>
                    <!--排除tomcat依赖-->
                    <exclusion>
                        <artifactId>spring-boot-starter-tomcat</artifactId>
                        <groupId>org.springframework.boot</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
			<dependency>
				<groupId>com.huaweicloud.sdk</groupId>
				<artifactId>huaweicloud-sdk-cdm</artifactId>
				<version>${huaweicloud-sdk-version}</version>
			</dependency>
			<dependency>
				<groupId>com.huaweicloud.sdk</groupId>
				<artifactId>huaweicloud-sdk-dataartsstudio</artifactId>
				<version>${huaweicloud-sdk-version}</version>
			</dependency>
			<dependency>
				<groupId>com.huaweicloud.sdk</groupId>
				<artifactId>huaweicloud-sdk-iam</artifactId>
				<version>${huaweicloud-sdk-version}</version>
			</dependency>
			<dependency>
				<groupId>com.huaweicloud.sdk</groupId>
				<artifactId>huaweicloud-sdk-dgc</artifactId>
				<version>${huaweicloud-sdk-version}</version>
			</dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <!--打包jar 与git commit 关联插件-->
            <plugin>
                <groupId>io.github.git-commit-id</groupId>
                <artifactId>git-commit-id-maven-plugin</artifactId>
                <version>${git.commit.plugin}</version>
            </plugin>
            <!--代码格式插件，默认使用spring 规则-->
            <plugin>
                <groupId>io.spring.javaformat</groupId>
                <artifactId>spring-javaformat-maven-plugin</artifactId>
                <version>${spring.checkstyle.plugin}</version>
            </plugin>
            <!-- 统一 revision 版本 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                    <updatePomFile>true</updatePomFile>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
