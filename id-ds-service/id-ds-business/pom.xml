<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
		<groupId>cn.teleinfo</groupId>
        <artifactId>id-ds-service</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>id-ds-business</artifactId>
    <packaging>jar</packaging>

    <description>核心业务处理模块</description>

    <dependencies>
		<!-- spring data jpa-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
        <!--business api、model 模块-->
        <dependency>
            <groupId>cn.teleinfo</groupId>
            <artifactId>id-ds-business-api</artifactId>
        </dependency>
        <!--文件管理-->
        <dependency>
            <groupId>cn.teleinfo</groupId>
            <artifactId>id-ds-common-oss</artifactId>
        </dependency>
        <!--feign 调用-->
        <dependency>
            <groupId>cn.teleinfo</groupId>
            <artifactId>id-ds-common-feign</artifactId>
        </dependency>
        <!--安全模块-->
        <dependency>
            <groupId>cn.teleinfo</groupId>
            <artifactId>id-ds-common-security</artifactId>
        </dependency>
        <!--日志处理-->
        <dependency>
            <groupId>cn.teleinfo</groupId>
            <artifactId>id-ds-common-log</artifactId>
        </dependency>
        <!--接口文档-->
        <dependency>
            <groupId>cn.teleinfo</groupId>
            <artifactId>id-ds-common-swagger</artifactId>
        </dependency>
        <!-- orm 模块-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <!--注册中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--配置中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!-- 阿里云短信下发 -->
        <dependency>
            <groupId>io.springboot.sms</groupId>
            <artifactId>aliyun-sms-spring-boot-starter</artifactId>
        </dependency>
        <!--xss 过滤-->
        <dependency>
            <groupId>cn.teleinfo</groupId>
            <artifactId>id-ds-common-xss</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
		<dependency>
			<groupId>com.h2database</groupId>
			<artifactId>h2</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
		</dependency>
		<dependency>
			<groupId>com.huaweicloud.sdk</groupId>
			<artifactId>huaweicloud-sdk-cdm</artifactId>
		</dependency>
		<dependency>
			<groupId>com.huaweicloud.sdk</groupId>
			<artifactId>huaweicloud-sdk-dataartsstudio</artifactId>
		</dependency>
		<dependency>
			<groupId>com.huaweicloud.sdk</groupId>
			<artifactId>huaweicloud-sdk-iam</artifactId>
		</dependency>
		<dependency>
			<groupId>com.huaweicloud.sdk</groupId>
			<artifactId>huaweicloud-sdk-dgc</artifactId>
		</dependency>
        <dependency>
            <groupId>cn.teleinfo</groupId>
            <artifactId>id-ds-quartz-api</artifactId>
            <version>3.8.3</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>boot</id>
        </profile>
        <profile>
            <id>cloud</id>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                    </plugin>
                    <plugin>
                        <groupId>io.fabric8</groupId>
                        <artifactId>docker-maven-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

	<build>
		<plugins>
			<plugin>
				<groupId>com.google.cloud.tools</groupId>
				<artifactId>jib-maven-plugin</artifactId>
				<configuration>
					<from>
						<image>harbor.idx.space/ops/openjdk:17.0-jdk</image>
						<!-- 指定所需系统架构 -->
						<platforms>
							<platform>
								<architecture>amd64</architecture>
								<os>linux</os>
							</platform>
							<platform>
								<architecture>arm64</architecture>
								<os>linux</os>
							</platform>
						</platforms>
					</from>
					<to>
						<image>harbor.idx.space/zadig/id-ds-business:latest</image>
					</to>
					<!--容器相关的属性-->
					<container>
						<!--jvm内存参数-->
						<environment>
							<JAVA_OPTS>-Xms1g -Xmx2g -Xss1024k</JAVA_OPTS>
						</environment>
						<entrypoint>
							<arg>/bin/bash</arg>
							<arg>-c</arg>
							<!--suppress UnresolvedMavenProperty -->
							<arg>java ${JAVA_OPTS} -Duser.timezone=PRC -cp /app/resources/:/app/classes/:/app/libs/* cn.teleinfo.ds.business.DsBusinessApplication
							</arg>
						</entrypoint>
						<ports>
							<port>6000</port>
						</ports>
						<creationTime>USE_CURRENT_TIMESTAMP</creationTime>
					</container>
					<allowInsecureRegistries>true</allowInsecureRegistries>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
