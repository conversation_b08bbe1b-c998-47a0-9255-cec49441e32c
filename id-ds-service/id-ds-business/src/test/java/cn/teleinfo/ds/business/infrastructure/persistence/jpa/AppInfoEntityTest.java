package cn.teleinfo.ds.business.infrastructure.persistence.jpa;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.AppInfoEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.AppInfoJpaRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@TestPropertySource(properties = {
		"spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1",
		"spring.datasource.driver-class-name=org.h2.Driver",
		"spring.datasource.username=sa",
		"spring.datasource.password=",
		"spring.jpa.show-sql=true"
})
class AppInfoEntityTest {

	@Autowired
	private AppInfoJpaRepository appInfoRepository;

	private AppInfoEntity createSampleAppInfo() {
		AppInfoEntity appInfoEntity = new AppInfoEntity();
		appInfoEntity.setSourceId(1L);
		appInfoEntity.setAppName("测试应用");
		appInfoEntity.setHandleCode("TEST_HANDLE");
		appInfoEntity.setDeployAddress("http://localhost:8080");
		appInfoEntity.setSysVersion("1.0.0");
		appInfoEntity.setEntPrefix("TEST");
		appInfoEntity.setProvincePrefix("BJ");
		appInfoEntity.setAppType(1);
		appInfoEntity.setCreateTime(LocalDateTime.now());
		appInfoEntity.setUpdateTime(LocalDateTime.now());
		return appInfoEntity;
	}

	@Test
	void testCreateApp() {
		AppInfoEntity appInfo = createSampleAppInfo();
		AppInfoEntity savedApp = appInfoRepository.save(appInfo);


		System.out.println(savedApp.getId());

		assertNotNull(savedApp.getId());
		assertEquals("测试应用", savedApp.getAppName());
		assertEquals("TEST_HANDLE", savedApp.getHandleCode());
	}

	@Test
	void testFindApp() {
		// 先保存一个实体
		AppInfoEntity appInfo = createSampleAppInfo();
		AppInfoEntity savedApp = appInfoRepository.save(appInfo);

		// 测试查询
		Optional<AppInfoEntity> foundApp = appInfoRepository.findById(savedApp.getId());
		assertTrue(foundApp.isPresent());
		assertEquals("测试应用", foundApp.get().getAppName());
	}

	@Test
	void testUpdateApp() {
		// 先保存一个实体
		AppInfoEntity appInfo = createSampleAppInfo();
		AppInfoEntity savedApp = appInfoRepository.save(appInfo);

		// 更新实体
		savedApp.setAppName("更新后的应用");
		savedApp.setSysVersion("2.0.0");
		AppInfoEntity updatedApp = appInfoRepository.save(savedApp);

		assertEquals("更新后的应用", updatedApp.getAppName());
		assertEquals("2.0.0", updatedApp.getSysVersion());
	}

	@Test
	void testDeleteApp() {
		// 先保存一个实体
		AppInfoEntity appInfo = createSampleAppInfo();
		AppInfoEntity savedApp = appInfoRepository.save(appInfo);

		// 删除实体
		appInfoRepository.delete(savedApp);

		// 验证删除结果
		Optional<AppInfoEntity> deletedApp = appInfoRepository.findById(savedApp.getId());
		assertTrue(deletedApp.isEmpty());
	}

	@Test
	void testFindAllApps() {
		// 保存多个实体
		appInfoRepository.save(createSampleAppInfo());
		appInfoRepository.save(createSampleAppInfo());
		appInfoRepository.save(createSampleAppInfo());

		// 查询所有
		List<AppInfoEntity> allApps = appInfoRepository.findAll();
		assertEquals(3, allApps.size());
	}
}
