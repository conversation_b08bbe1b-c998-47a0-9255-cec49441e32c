package cn.teleinfo.ds.business.handle;

import cn.teleinfo.ds.business.domain.model.aggregate.SharedTask;
import cn.teleinfo.ds.business.domain.model.entity.HandleDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleItemDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareChannel;
import cn.teleinfo.ds.business.domain.repository.HandlesRepository;
import cn.teleinfo.ds.business.domain.repository.ShareChannelRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;


@SpringBootTest
public class ShareTaskTest {
	@Autowired
	private HandlesRepository handlesRepository;
	@Autowired
	private ShareChannelRepository shareChannelRepository;

	@Test
	void genOutputTablesSQLTest(){
		HandleDomainEntity handle = handlesRepository.findByHandle("99.1000.1/YMZ12N98");

		for (HandleItemDomainEntity item : handle.getHandleItems()) {
			ShareChannel enableChannel = shareChannelRepository.findEnableChannel(item.getDataChannelId());
			item.setShareChannel(enableChannel);
		}


		SharedTask sharedTask = new SharedTask(null, List.of(handle), null, null,null);
		String sql = sharedTask.genOutputTablesSQL();
		System.out.println(sql);
	}
}
