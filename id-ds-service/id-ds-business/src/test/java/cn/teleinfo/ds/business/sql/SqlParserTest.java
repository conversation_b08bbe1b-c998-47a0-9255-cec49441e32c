package cn.teleinfo.ds.business.sql;

import com.pig4cloud.pig.common.core.util.SqlUtils;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.Join;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.select.SelectItem;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class SqlParserTest {

	// 提取 SQL 信息
	@Test
	void info() throws JSQLParserException {
		var sql = """
				select
						ai.id,
						concat('99.1000.1/YMZ12N98_', ai.id)  as APP_TID,
						concat('99.1000.1/YMZ12N73_', ds.id) as DATA_SERVICE_TID
				from yc_data_service ds inner join yc_app_info ai on ds.app_id = ai.id
				where ds.`is_deleted`= 0
						and ai.is_deleted = 0
				""";

		List<String> fields = SqlUtils.extractSelectSqlField(sql);
		if(fields != null){
			for (String string : fields) {
				log.info("field={}",string);
			}
		}

	}

}
