package cn.teleinfo.ds.business.huaweicloud;

import cn.hutool.json.JSONUtil;
import com.huaweicloud.sdk.core.auth.BasicCredentials;
import com.huaweicloud.sdk.core.auth.ICredential;
import com.huaweicloud.sdk.core.http.HttpConfig;
import com.huaweicloud.sdk.dataartsstudio.v1.DataArtsStudioClient;
import com.huaweicloud.sdk.dataartsstudio.v1.model.*;
import com.huaweicloud.sdk.dgc.v1.DgcClient;
import com.huaweicloud.sdk.dgc.v1.model.*;
import com.huaweicloud.sdk.dgc.v1.model.ScriptInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import java.util.List;

@Slf4j
public class HcsDgcTest {

    /**
     * AK
     */
    static String ak = "Y20RVDRARXXKDJNSYVRB";

    /**
     * SK
     */
    static String sk = "db3d1XxtHtwxXeAjpfapSu88Z26kdzjcNjtEZrvn";

    /**
     * 项目ID
     */
    static String projectId = "a24ee94724ea4bfa85f4c7938dc2e0a1";

    /**
     * 认证信息
     */
    static ICredential auth;

    /**
     * HTTP配置
     */
    static HttpConfig httpConfig;

    /**
     * 华为云HCS终端节点
     */
    static List<String> endpoints = List.of("https://dayu-dlf.cn-sdzy-1.sdgy.yc");

    /**
     * Dgc客户端
     */
    static DgcClient client;

    /**
     * 初始化参数
     */
    @BeforeAll
    static void beforeAll() {
        // 认证凭证
        auth = new BasicCredentials().withProjectId(projectId).withAk(ak).withSk(sk);
        // 禁用SSL校验
        httpConfig = HttpConfig.getDefaultHttpConfig();
        httpConfig.setIgnoreSSLVerification(true);
        // 构建Dgc客户端
        client = DgcClient.newBuilder()
                .withCredential(auth)
                .withHttpConfig(httpConfig)
                .withEndpoints(endpoints)
                .build();
        log.info("Dgc客户端创建成功");
    }

    /**
     * 查询脚本列表
     */
    @Test
    void listFactoryScripts() {
        ListScriptsRequest request = new ListScriptsRequest();
        request.withWorkspace("dd8652d44ba140799357ff8e2b790286");
        request.withLimit(20);
        request.withOffset(0);
        ListScriptsResponse response = client.listScripts(request);
        log.info(JSONUtil.toJsonPrettyStr(response));
    }

    /**
     * 查询脚本信息
     */
    @Test
    void showScript() {
        ShowScriptRequest request = new ShowScriptRequest();
        request.withWorkspace("dd8652d44ba140799357ff8e2b790286");
        request.withScriptName("tstd_yc_app_info");
        ShowScriptResponse response = client.showScript(request);
        log.info(JSONUtil.toJsonPrettyStr(response));
    }

    /**
     * 查询连接列表
     */
    @Test
    void listConnections() {
        ListConnectionsRequest request = new ListConnectionsRequest();
        request.withWorkspace("dd8652d44ba140799357ff8e2b790286");
        ListConnectionsResponse response = client.listConnections(request);
        log.info(JSONUtil.toJsonPrettyStr(response));
    }

    /**
     * 创建脚本
     */
    @Test
    void createScript() {
        CreateScriptRequest request = new CreateScriptRequest();
        request.withWorkspace("dd8652d44ba140799357ff8e2b790286");
        ScriptInfo body = new ScriptInfo();
        body.withContent("""
                INSERT OVERWRITE TABLE bsjx_std_test.tstd_yc_app_info
                SELECT id, created_by, created_time, updated_by, updated_time, app_name, handle_code, deploy_address,
                sys_version, ent_id, province_id, is_deleted, prefix_id, mp_dmm_app_id, app_type, master_data_scope, public_key\s
                FROM bsjx_stg_test.tstg_yc_app_info
                """);
        body.withDirectory("/STD");
        body.withType(ScriptInfo.TypeEnum.fromValue("HiveSQL"));
        body.withName("Test");
        body.withConnectionName("bsjx-hive-test");
        request.withBody(body);
        CreateScriptResponse response = client.createScript(request);
        log.info(JSONUtil.toJsonPrettyStr(response));
    }

    /**
     * 执行脚本
     */
    @Test
    void executeScript() {
        ExecuteScriptRequest request = new ExecuteScriptRequest();
        request.withWorkspace("dd8652d44ba140799357ff8e2b790286");
        request.withScriptName("tstd_yc_app_info");
        ExecuteScriptReq body = new ExecuteScriptReq();
        body.withParams("{}");
        request.withBody(body);
        ExecuteScriptResponse response = client.executeScript(request);
        log.info(JSONUtil.toJsonPrettyStr(response));
    }

    /**
     * 查询脚本实例执行结果
     */
    @Test
    void listScriptResults() {
        ListScriptResultsRequest request = new ListScriptResultsRequest();
        request.withWorkspace("dd8652d44ba140799357ff8e2b790286");
        request.withScriptName("tstd_yc_app_info");
        request.withInstanceId("e3c209ea-f6e2-4aa7-a819-77e49788d0de");
        ListScriptResultsResponse response = client.listScriptResults(request);
        log.info(JSONUtil.toJsonPrettyStr(response));
    }

    /**
     * 执行脚本，查询脚本实例执行结果
     */
    @Test
    void executeScriptListScriptResults() throws InterruptedException {
        // 执行脚本
        ExecuteScriptRequest executeScriptRequest = new ExecuteScriptRequest();
        executeScriptRequest.withWorkspace("dd8652d44ba140799357ff8e2b790286");
        executeScriptRequest.withScriptName("Test");
        ExecuteScriptReq body = new ExecuteScriptReq();
        body.withParams(null);
        executeScriptRequest.withBody(body);
        ExecuteScriptResponse executeScriptResponse = client.executeScript(executeScriptRequest);

        // 查询执行结果
        ListScriptResultsRequest listScriptResultsRequest = new ListScriptResultsRequest();
        listScriptResultsRequest.withWorkspace("dd8652d44ba140799357ff8e2b790286");
        listScriptResultsRequest.withScriptName("Test");
        listScriptResultsRequest.withInstanceId(executeScriptResponse.getInstanceId());
        ListScriptResultsResponse listScriptResultsResponse = client.listScriptResults(listScriptResultsRequest);
        while (true) {
            String status = listScriptResultsResponse.getStatus();
            if (status.equals("FINISHED")) {
                break;
            }
            listScriptResultsResponse = client.listScriptResults(listScriptResultsRequest);
            log.info(JSONUtil.toJsonPrettyStr(listScriptResultsResponse));
            Thread.sleep(2000);
        }
    }

}
