package cn.teleinfo.ds.business.huaweicloud;

import cn.hutool.json.JSONUtil;
import com.huaweicloud.sdk.cdm.v1.CdmClient;
import com.huaweicloud.sdk.cdm.v1.model.*;
import com.huaweicloud.sdk.core.auth.BasicCredentials;
import com.huaweicloud.sdk.core.auth.ICredential;
import com.huaweicloud.sdk.core.http.HttpConfig;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class HcsCdmTest {

    /**
     * AK
     */
    static String ak = "Y20RVDRARXXKDJNSYVRB";

    /**
     * SK
     */
    static String sk = "db3d1XxtHtwxXeAjpfapSu88Z26kdzjcNjtEZrvn";

    /**
     * 项目ID
     */
    static String projectId = "a24ee94724ea4bfa85f4c7938dc2e0a1";

    /**
     * 认证信息
     */
    static ICredential auth;

    /**
     * HTTP配置
     */
    static HttpConfig httpConfig;

    /**
     * 华为云HCS终端节点
     */
    static List<String> endpoints = List.of("https://cdm.cn-sdzy-1.sdgy.yc");

    /**
     * CDM客户端
     */
    static CdmClient client;

    /**
     * 初始化参数
     */
    @BeforeAll
    static void beforeAll() {
        // 认证凭证
        auth = new BasicCredentials().withProjectId(projectId).withAk(ak).withSk(sk);
        // 禁用SSL校验
        httpConfig = HttpConfig.getDefaultHttpConfig();
        httpConfig.setIgnoreSSLVerification(true);
        // 构建CDM客户端
        client = CdmClient.newBuilder()
                .withHttpConfig(httpConfig)
                .withCredential(auth)
                .withEndpoints(endpoints)
                .build();
        log.info("CDM客户端创建成功");


    }

    /**
     * 查询集群列表
     */
    @Test
    void listClusters() {
        ListClustersRequest request = new ListClustersRequest();
        ListClustersResponse response = client.listClusters(request);
        log.info(JSONUtil.toJsonPrettyStr(response));
    }

    /**
     * 查询集群详情
     */
    @Test
    void showClusterDetail() {
        ShowClusterDetailRequest request = new ShowClusterDetailRequest();
        request.withClusterId("eb76c76d-beeb-4eef-af87-ce645e6fc540");
        ShowClusterDetailResponse response = client.showClusterDetail(request);
        log.info(JSONUtil.toJsonPrettyStr(response));
    }


    /**
     * 查询作业（所有）
     */
    @Test
    void showJobsAll() {
        ShowJobsRequest request = new ShowJobsRequest();
        request.setClusterId("eb76c76d-beeb-4eef-af87-ce645e6fc540");
        request.setJobName("all");
        ShowJobsResponse response = client.showJobs(request);
        log.info(JSONUtil.toJsonPrettyStr(response));
    }

    /**
     * 查询作业(单个)
     */
    @Test
    void showJobs() {
        ShowJobsRequest request = new ShowJobsRequest();
        request.setClusterId("eb76c76d-beeb-4eef-af87-ce645e6fc540");
        request.setJobName("tstg_yc_app_info");
        ShowJobsResponse response = client.showJobs(request);
        log.info(JSONUtil.toJsonPrettyStr(response));
    }

    /**
     * 启动作业
     */
    @Test
    void startJob() {
        StartJobRequest request = new StartJobRequest();
        request.setClusterId("eb76c76d-beeb-4eef-af87-ce645e6fc540");
        request.setJobName("tstg_yc_app_info");
        StartJobResponse response = client.startJob(request);
        log.info(JSONUtil.toJsonPrettyStr(response));
    }

    /**
     * 查询作业执行状态
     */
    @Test
    void showJobStatus() {
        ShowJobStatusRequest request = new ShowJobStatusRequest();
        request.setClusterId("eb76c76d-beeb-4eef-af87-ce645e6fc540");
        request.setJobName("tstg_yc_app_info");
        ShowJobStatusResponse response = client.showJobStatus(request);
        log.info(JSONUtil.toJsonPrettyStr(response));
    }

    /**
     * 查询作业执行历史
     */
    @Test
    void ShowSubmissions() {
        ShowSubmissionsRequest request = new ShowSubmissionsRequest();
        request.setClusterId("eb76c76d-beeb-4eef-af87-ce645e6fc540");
        request.setJname("tstg_yc_app_info");
        ShowSubmissionsResponse response = client.showSubmissions(request);
        log.info(JSONUtil.toJsonPrettyStr(response));
    }

    /**
     * 查询连接
     */
    @Test
    void listConnections() {
        ShowLinkRequest request = new ShowLinkRequest();
        request.setClusterId("eb76c76d-beeb-4eef-af87-ce645e6fc540");
        request.setLinkName("all");
        ShowLinkResponse response = client.showLink(request);
        log.info(JSONUtil.toJsonPrettyStr(response));
    }

    /**
     * 指定集群创建作业
     */
    @Test
    void createJob() {
        CreateJobRequest request = new CreateJobRequest();
        request.withClusterId("eb76c76d-beeb-4eef-af87-ce645e6fc540");
        CdmCreateJobJsonReq body = new CdmCreateJobJsonReq();

        // 一、作业任务参数配置。例如配置作业失败重试、抽取并发数，具体可参考作业任务参数说明。
        List<Input> driverConfigsInputs = List.of(
                // 输入参数列表，列表中的每个参数为"name,value"结构，请参考inputs数据结构参数说明。
                new Input()
                        .withName("groupJobConfig.groupName")
                        .withValue("DEFAULT")
        );
        // 源连接参数、目的连接参数和作业任务参数，它们的配置数据结构相同，其中"inputs"里的参数不一样，详细请参见configs数据结构说明。
        List<Configs> driverConfigs = new ArrayList<>();
        driverConfigs.add(
                new Configs()
                        .withInputs(driverConfigsInputs)
                        .withName("groupJobConfig")
        );
        ConfigValues driverConfigValues = new ConfigValues();
        driverConfigValues.withConfigs(driverConfigs);

        // 二、源连接参数配置。根据不同源端有不同的参数配置，具体可参考源端作业参数说明下相应的源端参数配置。
        List<Input> fromConfigsInputs = List.of(
                new Input().withName("fromJobConfig.useSql").withValue("false"),
                new Input().withName("fromJobConfig.schemaName").withValue("id_yc_province"),
                new Input().withName("fromJobConfig.tableName").withValue("yc_app_info"),
                new Input().withName("fromJobConfig.columnList").withValue("id&created_by&created_time&updated_by&updated_time&app_name&handle_code&deploy_address&sys_version&ent_id&province_id&is_deleted&prefix_id&mp_dmm_app_id&app_type&master_data_scope&public_key"),
                new Input().withName("fromJobConfig.createOutTable").withValue("false")
        );
        
        List<Configs> fromConfigs = new ArrayList<>();
        fromConfigs.add(
                new Configs()
                        .withInputs(fromConfigsInputs)
                        .withName("fromJobConfig")
        );
        ConfigValues fromConfigValues = new ConfigValues();
        fromConfigValues.withConfigs(fromConfigs);

        // 三、目的连接参数配置。根据不同目的端有不同的参数配置，具体可参考目的端作业参数说明下相应的目的端参数配置。
        List<Input> toConfigsInputs = List.of(
                new Input().withName("toJobConfig.hive").withValue("hive"),
                new Input().withName("toJobConfig.database").withValue("bsjx_stg_test"),
                new Input().withName("toJobConfig.table").withValue("tstg_yc_app_info"),
                new Input().withName("toJobConfig.tablePreparation").withValue("CREATE_WHEN_NOT_EXIST"),
                new Input().withName("toJobConfig.columnList").withValue("id&created_by&created_time&updated_by&updated_time&app_name&handle_code&deploy_address&sys_version&ent_id&province_id&is_deleted&prefix_id&mp_dmm_app_id&app_type&master_data_scope&public_key"),
                new Input().withName("toJobConfig.convertNull").withValue("TO_NULL"),
                new Input().withName("toJobConfig.shouldClearTable").withValue("false"),
                new Input().withName("toJobConfig.csvDelimPolicy").withValue("DROP"),
                new Input().withName("toJobConfig.clearDataMode").withValue("TRUNCATE")
        );
        List<Configs> toConfigs = new ArrayList<>();
        toConfigs.add(
                new Configs()
                        .withInputs(toConfigsInputs)
                        .withName("toJobConfig")
        );
        ConfigValues toConfigValues = new ConfigValues();
        toConfigValues.withConfigs(toConfigs);
        // 四、创建作业
        List<Job> jobs = List.of(
                new Job()
                        // 作业类型：NORMAL_JOB：表/文件迁移、BATCH_JOB：整库迁移、SCENARIO_JOB：场景迁移。
                        .withJobType(Job.JobTypeEnum.fromValue("NORMAL_JOB"))
                        // 源端连接类型 generic-jdbc-connector：关系数据库连接 hive-connector：Hive连接 ...等
                        .withFromConnectorName("generic-jdbc-connector")
                        // 源连接参数配置
                        .withFromConfigValues(fromConfigValues)
                        // 源连接名称，即为通过"创建连接"接口创建的连接对应的连接名。
                        .withFromLinkName("bjsx-province-test")
                        // 目的端连接类型 generic-jdbc-connector：关系数据库连接 hive-connector：Hive连接 ...等
                        .withToConnectorName("hive-connector")
                        // 目的连接参数配置
                        .withToConfigValues(toConfigValues)
                        // 目的端连接名称，即为通过"创建连接"接口创建的连接对应的连接名。
                        .withToLinkName("bsjx-hive-test")
                        // 作业任务参数配置
                        .withDriverConfigValues(driverConfigValues)
                        // 作业名称，长度在1到240个字符之间。
                        .withName("mysql2hive")
        );
        body.withJobs(jobs);
        request.withBody(body);
        CreateJobResponse response = client.createJob(request);
        log.info(JSONUtil.toJsonPrettyStr(response));
    }

}
