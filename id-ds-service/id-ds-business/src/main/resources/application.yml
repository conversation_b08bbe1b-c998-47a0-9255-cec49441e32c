server:
  port: 6000

spring:
  application:
    name: @artifactId@
  cloud:
    nacos:
      username: nacos
      password: 47Ac5H78@YyNw
      discovery:
        server-addr: ${NACOS_HOST:*************}:${NACOS_PORT:8848}
        namespace: ${NACOS_NAMESPACE:public}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: ${spring.cloud.nacos.discovery.namespace}
  config:
    import:
      - nacos:<EMAIL>@.yml
      - nacos:${spring.application.name}-@profiles.active@.yml
  jpa:
    show-sql: true

# 雪花算法
id:
  snowflake:
    # 终端ID
    worker-id: 1
    # 数据中心ID
    datacenter-id: 1


