package cn.teleinfo.ds.business.domain.service;

import cn.teleinfo.ds.business.domain.model.aggregate.TargetSource;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceDomainEntity;
import com.pig4cloud.pig.common.core.util.PageResponse;

import java.util.Date;

public interface TargetSourceDomainService {

	/**
	 * 保存目标源
	 * @param targetSource 目标源聚合体
	 */
	void save(TargetSource targetSource);

	PageResponse<TargetSourceDomainEntity> listTargetSource(TargetSource targetSource, Date start, Date end, Integer page, Integer size);


	TargetSourceDomainEntity findById(Long id);
}
