package cn.teleinfo.ds.business.application.service;

import cn.teleinfo.ds.business.application.command.UpdateConnectionSettingCommand;
import cn.teleinfo.ds.business.domain.model.entity.SysConnectionDomainEntity;

public interface SysApplicationConnService {

	/**
	 * 查询连接设置
	 *
	 * @param platformType 平台类型
	 * @return 连接设置信息
	 */
	SysConnectionDomainEntity getConnectionSetting(Integer platformType);

	/**
	 * 更新连接设置
	 *
	 * @param command 连接设置信息
	 */
	void updateConnectionSetting(UpdateConnectionSettingCommand command);
}
