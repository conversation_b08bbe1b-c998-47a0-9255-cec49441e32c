package cn.teleinfo.ds.business.domain.model.aggregate;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.domain.model.entity.AppInfoDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.FieldSourceType;
import cn.teleinfo.ds.business.domain.model.entity.HandleDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleItemDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareDataSourcesItem;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDetail;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.SysConnectionDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceDomainEntity;
import com.pig4cloud.pig.common.core.exception.CheckedException;
import com.pig4cloud.pig.common.core.util.SqlUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 共享任务聚合跟
 */
@Slf4j
@Getter
public class SharedTask {

	// 共享任务
	private final SharedTaskDomainEntity sharedTask;

	// 对象标识
	private final List<HandleDomainEntity> handles;

	// 应用
	private final AppInfoDomainEntity appInfo;

	// 目标源
	private final TargetSourceDomainEntity targetSource;

	// 链接信息
	private final SysConnectionDomainEntity sysConnection;

	public SharedTask(SharedTaskDomainEntity sharedTask, List<HandleDomainEntity> handles,
					  AppInfoDomainEntity appInfo, TargetSourceDomainEntity targetSource, SysConnectionDomainEntity sysConnection) {
		if (sharedTask == null) {
			log.error("无共享任务，共享任务 id 错误");
			throw new CheckedException("无共享任务");
		}

		if (sysConnection == null) {
			log.error("系统未配置链接信息");
			throw new CheckedException("系统未配置链接信息");
		}

		if (handles == null || handles.isEmpty()) {
			log.error("无共享的对象标识 taskId={}", sharedTask.getId());
			throw new CheckedException("无共享的对象标识");
		}

		if (appInfo == null) {
			log.error("无应用，应用编码错误");
			throw new CheckedException("无应用");
		}

		if (appInfo.getShareDataSources() == null) {
			log.error("应用未配置共享源");
			throw new CheckedException("应用未配置共享源");
		}

		if (targetSource == null) {
			log.error("无目标源，目标源 id 错误");
			throw new CheckedException("无目标源");
		}

		// 过滤选择的字段
		// 购物车可能只选择部分字段
		// detail 内为选择字段
		// handle 内为全部字段
		List<HandleDomainEntity> resultHandles = new ArrayList<>();
		for (SharedTaskDetail detail : sharedTask.getDetails()) {
			for (HandleDomainEntity handle : handles) {
				if (StrUtil.equals(detail.getHandle(), handle.getHandle())) {
					HandleDomainEntity h = BeanUtil.copyProperties(handle, HandleDomainEntity.class);

					if (StrUtil.isEmpty(detail.getFields()) && !JSONUtil.isTypeJSON(detail.getFields())) {
						log.error("对象标识属性错误");
						throw new CheckedException("对象标识属性错误");
					}

					List<HandleItemDomainEntity> selectedItems = new ArrayList<>();

					// 已经选择字段
					List<String> selectedFields = JSONUtil.toList(detail.getFields(), String.class);
					for (String selectedField : selectedFields) {
						for (HandleItemDomainEntity handleItem : handle.getHandleItems()) {
							if (StrUtil.equals(selectedField, handleItem.getField())) {
								selectedItems.add(handleItem);
							}
						}
					}

					h.setHandleItems(selectedItems);
					resultHandles.add(h);
				}
			}
		}

		appInfo.getShareDataSources().setShareDataSourcesItem(JSONUtil.toBean(appInfo.getShareDataSources().getItems(), ShareDataSourcesItem.class));

		this.sharedTask = sharedTask;
		this.handles = resultHandles;
		this.appInfo = appInfo;
		this.targetSource = targetSource;
		this.sysConnection = sysConnection;
	}

	// 根据对象标识生成输出表 SQL
	public String genOutputTablesSQL() {
		StringBuilder sqlBuilder = new StringBuilder();

		for (HandleDomainEntity handle : this.handles) {
			// 99.1000.1/YMZ12N35 => YMZ12N35
			String masterTableName = handle.getHandle().substring(handle.getHandle().lastIndexOf("/") + 1);

			// 基础属性
			sqlBuilder.append("-- 创建【").append(handle.getName()).append("】基础属性表\n");
			sqlBuilder.append("DROP TABLE IF EXISTS TSTD_").append(masterTableName).append("_BASIC;\n");
			sqlBuilder.append("CREATE TABLE IF NOT EXISTS TSTD_").append(masterTableName).append("_BASIC (\n");

			// 基础属性
			List<HandleItemDomainEntity> basic = new ArrayList<>();

			// 扩展属性
			List<HandleItemDomainEntity> extend = new ArrayList<>();

			for (HandleItemDomainEntity item : handle.getHandleItems()) {
				if (FieldSourceType.BASIC.code() == item.getFieldSourceType()) {
					basic.add(item);
				}

				if (FieldSourceType.EXTEND.code() == item.getFieldSourceType()) {
					extend.add(item);
				}
			}

			if (basic.isEmpty()) {
				log.error("对象标识无基础属性 taskId={} handle={}", sharedTask.getId(), handle.getHandle());
				throw new CheckedException("对象标识无基础属性");
			}

			for (int i = 0; i < basic.size(); i++) {
				var item = basic.get(i);
				sqlBuilder.append("    ").append(item.getField()).append(" STRING COMMENT '").append(item.getField()).append("'");

				if (i < basic.size() - 1) {
					sqlBuilder.append(",\n");
				} else {
					sqlBuilder.append("\n");
				}
			}
			sqlBuilder.append(") COMMENT '").append(handle.getName()).append("基础属性表';\n\n");


			// 扩展属性 => 搞一个新表
			if (extend.isEmpty()) {
				log.warn("对象标识无扩展属性 taskId={} handle={}", sharedTask.getId(), handle.getHandle());
				continue;
			}

			for (HandleItemDomainEntity item : extend) {
				sqlBuilder.append("-- 创建【").append(handle.getName()).append("】扩展属性表\n");
				sqlBuilder.append("DROP TABLE IF EXISTS ").append("TSTD_").append(masterTableName).append("_EXTEND_").append(item.getField().toUpperCase()).append(";\n");
				sqlBuilder.append("CREATE TABLE IF NOT EXISTS TSTD_").append(masterTableName).append("_EXTEND_").append(item.getField().toUpperCase()).append(" (\n");

				if (item.getShareChannel() == null) {
					throw new CheckedException("扩展属性无共享通道");
				}

				var sql = StrUtil.isEmpty(item.getShareChannel().getEditSql()) ? item.getShareChannel().getAutoSql() : item.getShareChannel().getEditSql();

				List<String> fields = SqlUtils.extractSelectSqlField(sql);
				if (fields == null) {
					log.warn("扩展属性共享通道 SQL 错误。 taskId={} handle={} SQL={}", sharedTask.getId(), handle.getHandle(), sql);
					throw new CheckedException("扩展属性共享通道 SQL 错误");
				}

				for (int i = 0; i < fields.size(); i++) {
					String field = fields.get(i);
					if (i == 0) {
						sqlBuilder.append("    ").append(field).append(" STRING COMMENT '").append(field).append("'");
					} else {
						sqlBuilder.append(",\n    ").append(field).append(" STRING COMMENT '").append(field).append("'");
					}
				}

				sqlBuilder.append("\n) COMMENT '").append(handle.getName()).append("扩展属性表-").append(item.getField()).append("';\n\n");
			}

		}

		return sqlBuilder.toString();
	}
}
