package cn.teleinfo.ds.business.interfaces.dto.response;

import lombok.Data;

import java.util.List;

@Data
public class HandInfoResponse {
	private Long id;
	/**
	 * 企业前缀
	 */
	private String entPrefix;

	/**
	 * 通配符
	 */
	private String wildcard;

	/**
	 * 标识名称
	 */
	private String name;

	/**
	 * 标识
	 */
	private String handle;

	/**
	 * 实体类型 1业务实体 2资源实体
	 */
	private Integer entityType;

	/**
	 * 应用标识身份
	 */
	private String appHandleCode;

	/**
	 * 省级前缀
	 */
	private String provincePrefix;

	/**
	 * 属性
	 */
	private List<HandleItemResponse> items;

}
