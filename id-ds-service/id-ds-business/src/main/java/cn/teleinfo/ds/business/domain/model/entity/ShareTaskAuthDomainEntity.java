package cn.teleinfo.ds.business.domain.model.entity;


import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ShareTaskAuthDomainEntity {
	private Long id;
	/**
	 * 共享任务申请id
	 * <p>
	 * t_share_task_applications
	 */
	private Long shareTaskApplicationsId;
	/**
	 * 任务名称
	 */
	private String taskName;

	/**
	 * 任务编号
	 */
	private String taskNo;

	/**
	 * 任务类型(1：手动任务 2：定时任务)
	 */
	private Integer taskType;

	/**
	 * 授权状态( 1:申请中 2:已驳回 3:已授权)
	 */
	private Integer authStatus;

	/**
	 * 提交人
	 */
	private Long committedBy;

	/**
	 * 共享源类型
	 */
	private String sourceType;

	/**
	 * 目标源ID
	 */
	private Integer targetId;

	/**
	 * 根节点
	 */
	private String rootHandle;

	/**
	 * 省级前缀
	 */
	private String provincePrefix;

	/**
	 * 企业前缀
	 */
	private String entPrefix;

	/**
	 * 应用身份编码
	 */
	private String appHandleCode;

	/**
	 * 审核人ID
	 */
	private Long auditUserId;

	/**
	 * 审核备注
	 */
	private String auditRemark;

	/**
	 * 创建人
	 */
	private Long createBy;

	/**
	 * 更新人
	 */
	private Long updateBy;


	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * CRON表达式
	 */
	private String cronExpression;
}
