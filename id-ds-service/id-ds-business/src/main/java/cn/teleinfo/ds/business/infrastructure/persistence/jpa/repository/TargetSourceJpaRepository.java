package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.TargetSourceEntity;
import org.springframework.stereotype.Repository;

@Repository
public interface TargetSourceJpaRepository extends BaseRepository<TargetSourceEntity, Long> {

	TargetSourceEntity findFirstByAppHandleCodeAndTargetSourceName(String appHandleCode, String targetSourceName);
}
