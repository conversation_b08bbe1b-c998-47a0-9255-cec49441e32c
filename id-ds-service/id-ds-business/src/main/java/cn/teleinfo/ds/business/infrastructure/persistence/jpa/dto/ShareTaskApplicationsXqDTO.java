package cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@AllArgsConstructor
public class ShareTaskApplicationsXqDTO {

	private Long id;

	private String taskName;

	private String taskNo;

	private Integer taskType;

	private Integer targetSourceId;

	private Integer applicationsStatus;

	/**
	 * 操作时间
	 */
	private String updatedTime;

	/**
	 * 操作人
	 */
	private String updateByName;

	private String appHandleCode;

	private String entPrefix;


}
