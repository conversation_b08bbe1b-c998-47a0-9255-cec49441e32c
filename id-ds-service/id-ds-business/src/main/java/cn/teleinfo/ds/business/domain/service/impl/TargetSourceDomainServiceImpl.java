package cn.teleinfo.ds.business.domain.service.impl;

import cn.teleinfo.ds.business.domain.model.aggregate.TargetSource;
import cn.teleinfo.ds.business.domain.model.entity.SysConnectionDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceDomainEntity;
import cn.teleinfo.ds.business.domain.repository.TargetSourceRepository;
import cn.teleinfo.ds.business.domain.service.SysConnectionDomainService;
import cn.teleinfo.ds.business.domain.service.TargetSourceDomainService;
import com.pig4cloud.pig.common.core.exception.CheckedException;
import com.pig4cloud.pig.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
@AllArgsConstructor
public class TargetSourceDomainServiceImpl implements TargetSourceDomainService {
	private final TargetSourceRepository targetSourceRepository;
	private final SysConnectionDomainService sysConnectionDomainService;

	/**
	 * 保存目标源
	 *
	 * @param targetSource 目标源聚合体
	 */
	@Override
	@Transactional(rollbackFor = RuntimeException.class)
	public void save(TargetSource targetSource) {
		// 验证应用下目标源名称唯一。
		TargetSourceDomainEntity entity = targetSource.getTargetSourceDomainEntity();

		Integer count = targetSourceRepository.findByAppHandleCodeAndTargetSourceNameCount(targetSource.getAppHandleCode().appHandleCode(),
				entity.getTargetSourceName());
		if (count > 0) {
			throw new CheckedException("目标源名称重复！");
		}

		SysConnectionDomainEntity conn = sysConnectionDomainService.findByPlatformType(entity.getPlatformType());
		if (conn == null) {
			throw new CheckedException("未配置系统连接！");
		}

		targetSourceRepository.save(entity);
	}

	@Override
	public PageResponse<TargetSourceDomainEntity> listTargetSource(TargetSource targetSource, Date start, Date end, Integer page, Integer size) {
		TargetSourceDomainEntity entity = targetSource.getTargetSourceDomainEntity();
		return targetSourceRepository.listTargetSource(entity.getAppHandleCode(), entity.getTargetSourceName(),
				entity.getPlatformType(), start, end, page,size);
	}

	@Override
	public TargetSourceDomainEntity findById(Long id) {
		return targetSourceRepository.findById(id);
	}
}
