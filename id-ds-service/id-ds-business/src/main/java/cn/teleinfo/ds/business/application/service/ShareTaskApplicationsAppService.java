package cn.teleinfo.ds.business.application.service;

import cn.teleinfo.ds.business.application.command.sharetaskapplications.ShareTaskApplicationsCommand;
import cn.teleinfo.ds.business.application.query.HandleItemQuery;
import cn.teleinfo.ds.business.application.query.ShareTaskApplicationsQuery;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskApplicationDetails;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareTaskApplicationsView;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleApplicationResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleItemsResponse;
import com.pig4cloud.pig.common.core.util.PageResponse;

public interface ShareTaskApplicationsAppService {

	/**
	 * 创建共享任务申请
	 */
	void createShareTaskApplications(ShareTaskApplicationsCommand command);

	/**
	 * 修改共享任务申请
	 */
	void updateShareTaskApplications(Long applicationId, ShareTaskApplicationsCommand command);

	/**
	 * 列表共享任务申请
	 */
	PageResponse<ShareTaskApplicationsView> listShareTaskApplications(ShareTaskApplicationsQuery query);

	/**
	 * 获取共享任务申请详情
	 */
	ShareTaskApplicationDetails getShareTaskApplicationDetail(Long applicationId);

	/**
	 * 根据handle获取handleItems
	 *
	 * @param query handle
	 * @return handleItems
	 */
	HandleApplicationResponse getHandleItemsByHandle(HandleItemQuery query);
}
