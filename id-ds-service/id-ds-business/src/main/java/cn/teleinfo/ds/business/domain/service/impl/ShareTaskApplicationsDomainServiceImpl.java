package cn.teleinfo.ds.business.domain.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.teleinfo.ds.business.application.command.sharetaskapplications.ShareTaskApplicationsCommand;
import cn.teleinfo.ds.business.application.query.HandleItemQuery;
import cn.teleinfo.ds.business.application.query.ShareTaskApplicationsQuery;
import cn.teleinfo.ds.business.domain.model.aggregate.Handle;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskApplicationDetails;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskApplications;
import cn.teleinfo.ds.business.domain.model.entity.*;
import cn.teleinfo.ds.business.domain.model.valueobject.*;
import cn.teleinfo.ds.business.domain.repository.ShareTaskApplicationsDetailRepository;
import cn.teleinfo.ds.business.domain.repository.ShareTaskApplicationsRepository;
import cn.teleinfo.ds.business.domain.repository.ShareTaskAuthRepository;
import cn.teleinfo.ds.business.domain.service.HandleGraphDomainService;
import cn.teleinfo.ds.business.domain.service.ShareTaskApplicationsDetailsService;
import cn.teleinfo.ds.business.domain.service.ShareTaskApplicationsDomainService;
import cn.teleinfo.ds.business.domain.service.ShareTaskApplicationsService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareTaskApplicationsDetailDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareTaskApplicationsXqDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareTaskAuthDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareTaskApplicationsEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareTaskApplicationsView;
import cn.teleinfo.ds.business.interfaces.dto.request.CartRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleApplicationResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleItemDetailResponse;
import cn.teleinfo.ds.upms.api.entity.SysLog;
import cn.teleinfo.ds.upms.api.entity.SysRole;
import cn.teleinfo.ds.upms.api.feign.RemoteLogService;
import cn.teleinfo.ds.upms.api.feign.RoleService;
import cn.teleinfo.ds.upms.api.vo.RoleCommonVO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pig4cloud.pig.common.core.constant.UserConstants;
import com.pig4cloud.pig.common.core.exception.CheckedException;
import com.pig4cloud.pig.common.core.util.PageResponse;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.common.security.service.PigUser;
import com.pig4cloud.pig.common.security.util.SecurityUtils;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

@Service
@AllArgsConstructor
public class ShareTaskApplicationsDomainServiceImpl implements ShareTaskApplicationsDomainService {

	private final ShareTaskApplicationsRepository repository;

	private final ShareTaskApplicationsService service;

	private final ShareTaskApplicationsDetailsService detailsService;

	private final ShareTaskAuthRepository authRepository;

	private final ShareTaskApplicationsDetailRepository detailRepository;

	private final HandleGraphDomainService handleGraphDomainService;

	private final RoleService roleService;

	/**
	 * 创建任务申请
	 *
	 * @param command 任务申请信息
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void createShareTaskApplications(ShareTaskApplicationsCommand command) {
		ShareTaskApplications shareTaskApplications = service.createShareTaskApplications(command);
		ShareTaskApplicationDomain valueObject = new ShareTaskApplicationDomain();
		BeanUtils.copyProperties(shareTaskApplications, valueObject);
		valueObject.setTaskNo(UUID.randomUUID().toString());
		ShareTaskApplicationsEntity applications = repository.createShareTaskApplicationsEntity(valueObject);

		if (CollectionUtil.isNotEmpty(command.getCart())) {
			ArrayList<ShareTaskApplicationsDetailsValueObject> detailsEntities = new ArrayList<>();
			for (CartRequest cartRequest : command.getCart()) {
				Cart cart = detailsService.setCartObject(cartRequest);
				ShareTaskApplicationsDetailsValueObject detailsEntity = new ShareTaskApplicationsDetailsValueObject();
				BeanUtils.copyProperties(shareTaskApplications, detailsEntity);
				detailsEntity.setHandle(cart.getHandle());
				detailsEntity.setFields(cart.getFields());
				detailsEntity.setShareTaskApplicationsId(applications.getId());
				detailsEntities.add(detailsEntity);
			}
			repository.createShareTaskApplicationsDetails(detailsEntities);
		}
	}

	/**
	 * 修改任务申请
	 *
	 * @param applicationId 申请id
	 * @param command       任务申请信息
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateShareTaskApplications(Long applicationId, ShareTaskApplicationsCommand command) {
		ShareTaskApplicationsDomainEntity domainEntity = repository.findById(applicationId);
		command.setUpdate(true);
		ShareTaskApplications shareTaskApplications = service.createShareTaskApplications(command);
		ShareTaskApplicationDomain valueObject = setShareTaskApplicationDomain(applicationId, shareTaskApplications, domainEntity);
		// 修改申请信息
		repository.createShareTaskApplicationsEntity(valueObject);
		// 删除申请信息
		repository.deleteShareTaskApplicationsDetailsEntityS(command);
		// 新增申请详情信息
		if (CollectionUtil.isNotEmpty(command.getCart())) {
			ArrayList<ShareTaskApplicationsDetailsValueObject> detailsEntities = new ArrayList<>();
			for (CartRequest cartRequest : command.getCart()) {
				Cart cart = detailsService.setCartObject(cartRequest);
				ShareTaskApplicationsDetailsValueObject detailsEntity = new ShareTaskApplicationsDetailsValueObject();
				BeanUtils.copyProperties(shareTaskApplications, detailsEntity);
				detailsEntity.setHandle(cart.getHandle());
				detailsEntity.setFields(cart.getFields());
				detailsEntity.setShareTaskApplicationsId(applicationId);
				detailsEntities.add(detailsEntity);
			}
			repository.createShareTaskApplicationsDetails(detailsEntities);
		}
	}

	@NotNull
	private static ShareTaskApplicationDomain setShareTaskApplicationDomain(Long applicationId, ShareTaskApplications shareTaskApplications, ShareTaskApplicationsDomainEntity domainEntity) {
		ShareTaskApplicationDomain valueObject = new ShareTaskApplicationDomain();
		BeanUtils.copyProperties(shareTaskApplications, valueObject);
		valueObject.setId(applicationId);
		valueObject.setCreateTime(domainEntity.getCreateTime());
		valueObject.setTaskNo(domainEntity.getTaskNo());
		valueObject.setApplicationsStatus(domainEntity.getApplicationsStatus());
		valueObject.setCreateBy(domainEntity.getCreateBy());
		return valueObject;
	}

	/**
	 * 查询任务列表
	 *
	 * @return 分页查询结果
	 */
	@Override
	public PageResponse<ShareTaskApplicationsView> listShareTaskApplications(ShareTaskApplicationsQuery query) {

		Long id = SecurityUtils.getUser().getId();
		R<List<RoleCommonVO>> userList = roleService.getRoleListByUserId(id);
		if (ObjectUtil.isNotNull(userList.getData())) {
			RoleCommonVO sysRole = userList.getData().get(0);

			if (!StrUtil.equals(UserConstants.USER_ADMIN_CODE, sysRole.getRoleCode())) {
				query.setAppHandleCode(sysRole.getAppHandleCode());
			}
		}
		return repository.listShareTaskApplications(query, query.getPage(), query.getSize());
	}

	@Override
	public ShareTaskApplicationDetails getShareTaskApplicationDetail(Long applicationId) {
		// 获取申请信息
		ShareTaskApplicationDetails details = new ShareTaskApplicationDetails();
		ShareTaskApplicationsXqDTO dto = repository.findShareTaskApplicationsById(applicationId);
		if (ObjectUtil.isEmpty(dto)) {
			return details;
		}
		ShareTaskApplicationValue applicationValue = new ShareTaskApplicationValue();
		BeanUtils.copyProperties(dto, applicationValue);
		details.setBasicInfo(applicationValue);
		// 获取申请信息
		List<ShareTaskAuthDTO> auths = authRepository.findShareTaskAuthsByApplicationId(dto.getId());
		if (CollectionUtil.isNotEmpty(auths)) {
			ArrayList<ShareTaskAuthApplicationValue> values = new ArrayList<>();
			for (ShareTaskAuthDTO auth : auths) {
				ShareTaskAuthApplicationValue value = new ShareTaskAuthApplicationValue();
				BeanUtils.copyProperties(auth, value);
				values.add(value);
			}
			details.setAuthorizeRecords(values);
		}

		List<ShareTaskApplicationsDetailDTO> detailDTOS = detailRepository.findShareTaskApplicationsDetailsByTaskId(applicationId);
		if (CollectionUtil.isEmpty(detailDTOS)) {
			return details;
		}
		// 获取共享数据
		ArrayList<ShareTaskApplicationDetailValue> detailValues = new ArrayList<>();
		detailDTOS.forEach(detail -> {
			ShareTaskApplicationDetailValue value = new ShareTaskApplicationDetailValue();
			BeanUtils.copyProperties(detail, value);
			detailValues.add(value);
		});
		details.setShareData(detailValues);
		return details;
	}

	/**
	 * 查询一个共享任务申请的详情
	 *
	 * @param shareTaskApplicationsId 共享任务申请id
	 * @return 共享任务申请的详情
	 */
	@Override
	public ShareTaskApplicationsDomainEntity findShareTaskApplicationsDomainEntity(ShareTaskApplicationsId shareTaskApplicationsId) {
		ShareTaskApplicationsDomainEntity entity = repository.findShareTaskApplicationsDomainEntity(shareTaskApplicationsId.id());
		if (entity == null) {
			throw new CheckedException("共享任务申请不存在，请检查");
		}
		List<ShareTaskApplicationsDetailsDomainEntity> details = detailRepository.findShareTaskApplicationsDetailsDomainEntities(shareTaskApplicationsId.id());
		entity.setDetailsDomainEntities(details);
		return entity;
	}

	/**
	 * 更新共享任务申请状态
	 *
	 * @param id         id
	 * @param authStatus 授权状态
	 */
	@Override
	public void updateApplicationsStatus(Long id, AuthStatus authStatus) {
		repository.updateStatusById(id, authStatus.code());
	}

	@Override
	public HandleApplicationResponse getHandleItemsByHandle(HandleItemQuery query) {
		Handle handle = handleGraphDomainService.handleItems(query.getHandle());
		List<HandleItemDomainEntity> modelEntities = handle.getHandleItemModelEntities();
		ShareTaskApplicationsDetailsDomainEntity detailsDomainEntity = detailRepository.findShareTaskApplicationsDetailsById(query.getShareTaskApplicationsDetailId());
		if (ObjectUtil.isEmpty(detailsDomainEntity)) {
			return null;
		}
		String fields = detailsDomainEntity.getFields();
		ObjectMapper mapper = new ObjectMapper();
		String[] array;
		try {
			array = mapper.readValue(fields, String[].class);
		} catch (JsonProcessingException e) {
			throw new RuntimeException(e);
		}
		List<String> list = Arrays.asList(array);

		return filterEntitiesByFields(modelEntities, list);
	}


	/**
	 * 根据字段过滤实体
	 *
	 * @param modelEntities 实体列表
	 * @param filterList    需要过滤的字段列表
	 * @return 过滤后的实体列表
	 */
	public HandleApplicationResponse filterEntitiesByFields(
			List<HandleItemDomainEntity> modelEntities,
			List<String> filterList) {
		List<HandleItemDomainEntity> entityList = modelEntities.stream()
				.filter(entity -> filterList.contains(entity.getField()))
				.toList();
		if (CollectionUtil.isEmpty(entityList)) {
			return null;
		}
		// 基础属性
		List<HandleItemDomainEntity> isFoundation = entityList.stream().filter(item -> item.getFieldSourceType() == 0).toList();
		// 扩展属性
		List<HandleItemDomainEntity> isExtend = entityList.stream().filter(item -> item.getFieldSourceType() == 1).toList();
		List<HandleItemDetailResponse> items = isFoundation.stream()
				.map(entity -> {
					HandleItemDetailResponse dto = new HandleItemDetailResponse();
					BeanUtils.copyProperties(entity, dto);
					return dto;
				}).toList();
		List<HandleItemDetailResponse> extendItems = isExtend.stream()
				.map(entity -> {
					HandleItemDetailResponse dto = new HandleItemDetailResponse();
					BeanUtils.copyProperties(entity, dto);
					return dto;
				}).toList();
		HandleApplicationResponse response = new HandleApplicationResponse();
		response.setItems(items);
		response.setExtendItems(extendItems);
		return response;
	}

}
