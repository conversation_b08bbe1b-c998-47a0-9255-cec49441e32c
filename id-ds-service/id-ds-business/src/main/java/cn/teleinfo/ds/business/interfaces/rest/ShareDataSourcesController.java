package cn.teleinfo.ds.business.interfaces.rest;


import cn.teleinfo.ds.business.application.command.CreateShareDataSourcesCommand;
import cn.teleinfo.ds.business.application.query.ListShareDataSourcesQuery;
import cn.teleinfo.ds.business.application.service.ShareDataSourcesApplicationService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareSourceDTO;
import cn.teleinfo.ds.business.interfaces.assembler.ShareDataSourcesAssembler;
import cn.teleinfo.ds.business.interfaces.dto.request.CreateShareDataSourcesRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.ListShareDataSourcesRequest;
import com.pig4cloud.pig.common.core.util.PageResponse;
import com.pig4cloud.pig.common.core.util.R;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 共享源管理
 * 数据源管理
 */
@RestController
@RequestMapping("/share-data-sources")
@AllArgsConstructor
public class ShareDataSourcesController {
	private final ShareDataSourcesApplicationService shareDataSourcesApplicationService;
	private final ShareDataSourcesAssembler shareDataSourcesAssembler;

	/**
	 * 创建共享数据源
	 *
	 * @param request 共享数据源信息
	 */
	@PostMapping
	public R createShareDataSources(@RequestBody @Valid CreateShareDataSourcesRequest request) {
		CreateShareDataSourcesCommand command = shareDataSourcesAssembler.toCreateShareDataSourcesCommand(request);
		shareDataSourcesApplicationService.createShareDataSources(command);
		return R.ok();
	}


	@GetMapping
	public R<PageResponse<ShareSourceDTO>> listShareDataSources(@Valid ListShareDataSourcesRequest request) {
		ListShareDataSourcesQuery query = shareDataSourcesAssembler.toListShareDataSourcesQuery(request);
		return R.ok(shareDataSourcesApplicationService.listShareDataSources(query));
	}

}
