package cn.teleinfo.ds.business.application.service;

import cn.teleinfo.ds.business.application.query.ListHandlesQuery;
import cn.teleinfo.ds.business.domain.model.aggregate.Handle;
import cn.teleinfo.ds.business.domain.model.aggregate.HandleDirectory;
import cn.teleinfo.ds.business.domain.model.entity.HandleDomainEntity;
import com.pig4cloud.pig.common.core.util.PageResponse;

import java.util.List;

public interface HandlesApplicationService {

	/**
	 * 对象标识列表
	 */
	PageResponse<Handle> listHandles(ListHandlesQuery query);

	/**
	 * 查询对象标识
	 */
	HandleDomainEntity findByHandle(String handle);

	List<HandleDirectory> directory();

}
