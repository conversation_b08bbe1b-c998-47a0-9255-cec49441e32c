package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareSourceDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareSourceEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareSourceView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

@Repository
public interface ShareSourceJpaRepository extends BaseRepository<ShareSourceEntity, Long> {

	@Query(nativeQuery = true,
			value = """
					SELECT
					    s.id,
					    s.platform_type AS platformType,
					    s.conn_state AS connState,
					    s.app_handle_code AS appHandleCode,
					    a.app_name AS appName,
					    s.update_by AS updateBy,
					    s.create_time AS createTime,
					    s.update_time AS updateTime,
					    u.`name` AS updateByName
					FROM t_share_source s , t_app_info a, sys_user u
					WHERE s.app_handle_code = :appHandleCode
					AND s.app_handle_code = a.handle_code
					AND IF(:appName != '' AND :appName is not null, a.app_name like CONCAT('%',:appName,'%'), 1=1 )
					AND IF(:platformType IS NOT NULL, s.platform_type = :platformType, 1=1 )
					AND IF(:connState IS NOT NULL, s.conn_state = :connState, 1=1 )
					AND IF(:startDate IS NOT NULL AND :endDate IS NOT NULL, s.update_time BETWEEN :startDate AND :endDate , 1=1)
					AND s.update_by = u.user_id
					AND s.is_deleted = 0
					""",
			countQuery =
					"""
							SELECT
								count(*)
							FROM t_share_source s , t_app_info a, sys_user u
							WHERE s.app_handle_code = :appHandleCode
							AND s.app_handle_code = a.handle_code
							AND IF(:appName != '' AND :appName is not null, a.app_name like CONCAT('%',:appName,'%'), 1=1 )
							AND IF(:platformType IS NOT NULL, s.platform_type = :platformType, 1=1 )
							AND IF(:connState IS NOT NULL, s.conn_state = :connState, 1=1 )
							AND IF(:startDate IS NOT NULL AND :endDate IS NOT NULL, s.update_time BETWEEN :startDate AND :endDate , 1=1)
							AND s.update_by = u.user_id
							AND s.is_deleted = 0
							"""
	)
	Page<ShareSourceView> listShareDataSources(@Param("appHandleCode") String appHandleCode,
											   @Param("appName") String appName,
											   @Param("platformType") Integer platformType,
											   @Param("connState") Integer connState,
											   @Param("startDate") LocalDateTime startDate,
											   @Param("endDate") LocalDateTime endDate,
											   Pageable pageable);

	ShareSourceEntity findFirstByAppHandleCode(String appHandleCode);
}