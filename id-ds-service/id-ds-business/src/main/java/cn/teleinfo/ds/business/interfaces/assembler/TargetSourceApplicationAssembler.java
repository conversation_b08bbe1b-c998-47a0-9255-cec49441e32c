package cn.teleinfo.ds.business.interfaces.assembler;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.application.command.CreateTargetSourceCommand;
import cn.teleinfo.ds.business.application.query.ListTargetSourceQuery;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceDomainEntity;
import cn.teleinfo.ds.business.interfaces.dto.request.ListTargetSourceRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.CreateTargetSourceRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.ListTargetSourceResponse;
import com.pig4cloud.pig.common.core.util.PageResponse;
import jakarta.validation.Valid;
import org.springframework.stereotype.Component;

@Component
public class TargetSourceApplicationAssembler {

	public CreateTargetSourceCommand toCreateTargetSourceCommand(CreateTargetSourceRequest request) {
		return BeanUtil.copyProperties(request, CreateTargetSourceCommand.class);
	}

	public ListTargetSourceQuery toListTargetSourceQuery(@Valid ListTargetSourceRequest request) {
		return BeanUtil.copyProperties(request, ListTargetSourceQuery.class);
	}

	public PageResponse<ListTargetSourceResponse> toListTargetSourceResponse(PageResponse<TargetSourceDomainEntity> response) {
		var records = response.getRecords().stream().map(t -> BeanUtil.copyProperties(t, ListTargetSourceResponse.class)).toList();
		return new PageResponse<>(records, response.getTotal(), response.getSize(), response.getCurrent(), response.getPages());
	}
}
