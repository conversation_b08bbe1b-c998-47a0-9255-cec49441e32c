package cn.teleinfo.ds.business.application.service.impl;

import cn.teleinfo.ds.business.application.query.SharedTaskInstanceListQuery;
import cn.teleinfo.ds.business.application.service.SharedTaskInstanceAppService;
import cn.teleinfo.ds.business.domain.service.SharedTaskInstanceDomainService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskInstanceListView;
import cn.teleinfo.ds.business.interfaces.dto.response.SharedTaskInstanceDetailResponse;
import com.pig4cloud.pig.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@AllArgsConstructor
@Service
public class SharedTaskInstanceAppServiceImpl implements SharedTaskInstanceAppService {

	private final SharedTaskInstanceDomainService service;

	@Override
	public PageResponse<SharedTaskInstanceListView> listSharedTaskInstances(SharedTaskInstanceListQuery query) {
		return service.listSharedTaskInstances(query);
	}

	@Override
	public SharedTaskInstanceDetailResponse getShareTaskInstanceDetail(Long instanceId) {
		return service.getShareTaskInstanceDetail(instanceId);
	}
}
