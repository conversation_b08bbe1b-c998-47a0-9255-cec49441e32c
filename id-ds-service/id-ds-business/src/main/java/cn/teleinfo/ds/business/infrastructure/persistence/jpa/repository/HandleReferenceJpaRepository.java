package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.HandleReferenceEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface HandleReferenceJpaRepository extends BaseRepository<HandleReferenceEntity, Long> {
	List<Long> findIdBy();

	List<HandleReferenceEntity> findByHandleItemId(Long id);
}