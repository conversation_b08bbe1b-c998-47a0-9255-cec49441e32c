package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.*;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareChannelEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsApplicationsDetailView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsApplicationsView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsVersionSqlView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ShareChannelJpaRepository extends BaseRepository<ShareChannelEntity, Long> {
	@Query(nativeQuery = true,
			value = "SELECT " +
					"t1.id, " +
					"t1.share_channel_id as shareChannelId, " +
					"t3.id as dataChannelId, " +
					"t3.data_channel_name as shareChannelName, " +
					"t1.object_handle as handle, " +
					"t3.data_type as dataType, " +
					"t1.main_version as mainVersion, " +
					"t1.minor_version as minorVersion, " +
					"t1.update_time as updatedTime, " +
					"t1.status as status " +
					"FROM t_share_channel t1 " +
					"LEFT JOIN t_share_channel t2 ON t1.share_channel_id = t2.share_channel_id " +
					"AND ((t2.enable = 1 AND t1.enable != 1) OR (t1.enable = t2.enable AND (t2.main_version > t1.main_version OR (t2.main_version = t1.main_version AND t2.minor_version > t1.minor_version)))) " +
					"AND t2.is_deleted = 0 " +
					"LEFT JOIN t_data_channel t3 ON t1.data_channel_id = t3.id " +
					"WHERE t1.is_deleted = 0 " +
					"AND IF(:channelName != '' AND :channelName is not null, t3.data_channel_name like CONCAT('%',:channelName,'%'), 1=1 ) " +
					"AND IF(:handle != '' AND :handle is not null, t1.object_handle like CONCAT('%',:handle,'%'), 1=1 ) " +
					"AND IF(:status != '' AND :status is not null, t1.status  = :status, 1=1 ) " +
					"AND IF(:startTime IS NOT NULL AND :endTime IS NOT NULL, t1.update_time BETWEEN :startTime AND :endTime , 1=1) " +
					"AND IF(:appHandle != '' AND :appHandle is not null, t1.app_handle_code like CONCAT('%',:appHandle,'%'), 1=1 ) " +
					"AND IF(:userAppHandle != '' AND :userAppHandle is not null, t1.app_handle_code like CONCAT('%',:userAppHandle,'%'), 1=1 ) " +
					"AND t2.id IS NULL ",
			countQuery = "SELECT count(1) " +
					"FROM t_share_channel t1 " +
					"LEFT JOIN t_share_channel t2 ON t1.share_channel_id = t2.share_channel_id " +
					"AND ((t2.enable = 1 AND t1.enable != 1) OR (t1.enable = t2.enable AND (t2.main_version > t1.main_version OR (t2.main_version = t1.main_version AND t2.minor_version > t1.minor_version)))) " +
					"AND t2.is_deleted = 0 " +
					"LEFT JOIN t_data_channel t3 ON t1.data_channel_id = t3.id " +
					"WHERE t1.is_deleted = 0 " +
					"AND IF(:channelName != '' AND :channelName is not null, t3.data_channel_name like CONCAT('%',:channelName,'%'), 1=1 ) " +
					"AND IF(:handle != '' AND :handle is not null, t1.object_handle like CONCAT('%',:handle,'%'), 1=1 ) " +
					"AND IF(:status != '' AND :status is not null, t1.status = :status, 1=1 ) " +
					"AND IF(:startTime IS NOT NULL AND :endTime IS NOT NULL, t1.update_time BETWEEN :startTime AND :endTime , 1=1) " +
					"AND IF(:appHandle != '' AND :appHandle is not null, t1.app_handle_code like CONCAT('%',:appHandle,'%'), 1=1 ) " +
					"AND IF(:userAppHandle != '' AND :userAppHandle is not null, t1.app_handle_code like CONCAT('%',:userAppHandle,'%'), 1=1 ) " +
					"AND t2.id IS NULL "
	)
	Page<ShareChannelsDTO> listShareChannels(@Param("channelName") String channelName,
											 @Param("handle") String handle,
											 @Param("status") String status,
											 @Param("startTime") LocalDateTime startTime,
											 @Param("endTime") LocalDateTime endTime,
											 @Param("appHandle") String appHandle,
											 @Param("userAppHandle") String userAppHandle,
											 Pageable pageable);

	@Query(nativeQuery = true, value = "select " +
			"a.id as id, " +
			"a.share_channel_id as shareChannelId, " +
			"d.id as dataChannelId, " +
			"d.data_channel_name as shareChannelName, " +
			"a.object_handle as handle, " +
			"d.data_type as dataType, " +
			"a.main_version as mainVersion, " +
			"a.minor_version as minorVersion, " +
			"b.app_name as appName, " +
			"c.org_name as entName, " +
			"a.status as status, " +
			"a.auto_sql as autoSql, " +
			"a.edit_sql as editSql " +
			"from t_share_channel a " +
			"left join t_app_info b on a.app_handle_code = b.handle_code " +
			"left join t_ent_prefix c on b.ent_prefix = c.ent_prefix " +
			"left join t_data_channel d ON a.data_channel_id = d.id " +
			"where a.id = :id " +
			"and a.is_deleted = 0 ")
	ShareChannelsDetailsDTO queryDetailsByShareChannelIdAndStatus(@Param("id") String id);

	@Query(nativeQuery = true, value = "select " +
			"auto_sql as autoSql, " +
			"edit_sql as editSql " +
			"from t_share_channel " +
			"where share_channel_id = :shareChannelId and main_version = :main_version " +
			"and minor_version = :minor_version " +
			"and is_deleted = 0 ")
	ShareChannelsVersionSqlView queryVersionSql(@Param("shareChannelId") String shareChannelId,
													  @Param("main_version") String main_version,
													  @Param("minor_version") String minor_version);

	@Query(nativeQuery = true, value = "select " +
			"a.id as id, " +
			"a.share_channel_id as shareChannelId, " +
			"d.id as dataChannelId, " +
			"d.data_channel_name as shareChannelName, " +
			"a.object_handle as handle, " +
			"d.data_type as dataType, " +
			"a.main_version as mainVersion, " +
			"a.minor_version as minorVersion, " +
			"b.app_name as appName, " +
			"c.org_name as entName, " +
			"a.status as status, " +
			"a.auto_sql as autoSql, " +
			"a.edit_sql as editSql " +
			"from t_share_channel a " +
			"left join t_app_info b on a.app_handle_code = b.handle_code " +
			"left join t_ent_prefix c on a.ent_prefix = c.ent_prefix " +
			"left join t_data_channel d ON a.data_channel_id = d.id " +
			"where a.share_channel_id = :shareChannelId " +
			"and a.is_deleted = 0 ")
	List<ShareChannelsDetailsDTO> listShareChannelsVersion(@Param("shareChannelId") String shareChannelId);

	@Query(nativeQuery = true, value = "select " +
			"a.id as id, " +
			"a.share_channel_id as shareChannelId, " +
			"d.id as dataChannelId, " +
			"d.data_channel_name as shareChannelName, " +
			"a.object_handle as handle, " +
			"d.data_type as dataType, " +
			"a.main_version as mainVersion, " +
			"a.minor_version as minorVersion, " +
			"b.app_name as appName, " +
			"c.org_name as entName, " +
			"a.status as status, " +
			"a.auto_sql as autoSql, " +
			"a.edit_sql as editSql, " +
			"a.change_reason as changeReason, " +
			"a.enable as enable, " +
			"f.channel_status as channelStatus, " +
			"e.name as editorUser, " +
			"a.update_time as updatedTime " +
			"from t_share_channel a " +
			"left join t_app_info b on a.app_handle_code = b.handle_code " +
			"left join t_ent_prefix c on a.ent_prefix = c.ent_prefix " +
			"left join t_data_channel d ON a.data_channel_id = d.id " +
			"left join sys_user e ON a.update_by = e.user_id " +
			"left join t_share_channel_applications f on a.share_channel_id = f.share_channel_id " +
			"and a.main_version = f.main_version and a.minor_version = f.minor_version and f.is_deleted = 0 " +
			"where a.share_channel_id = :shareChannelId " +
			"and a.is_deleted = 0 " +
			"order by a.main_version desc, a.minor_version desc ")
	List<ShareChannelsVersionDTO> queryShareChannelsVersionDTO(@Param("shareChannelId") String shareChannelId);

	@Query(nativeQuery = true,
			value = "SELECT " +
					"t1.id, " +
					"t1.share_channel_id as shareChannelId, " +
					"t3.id as dataChannelId, " +
					"t2.id as applicationId, " +
					"t3.data_channel_name as shareChannelName, " +
					"t1.object_handle as handle, " +
					"t3.data_type as dataType, " +
					"t1.main_version as mainVersion, " +
					"t1.minor_version as minorVersion, " +
					"t2.created_time as updatedTime, " +
					"t2.channel_status as status " +
					"FROM t_share_channel t1 " +
					"INNER JOIN t_share_channel_applications t2 ON t1.share_channel_id = t2.share_channel_id " +
					"AND t1.main_version = t2.main_version AND t1.minor_version = t2.minor_version " +
					"LEFT JOIN t_data_channel t3 ON t1.data_channel_id = t3.id " +
					"WHERE t1.is_deleted = 0 and t2.is_deleted = 0 " +
					"AND IF(:channelName != '' AND :channelName is not null, t3.data_channel_name like CONCAT('%',:channelName,'%'), 1=1 ) " +
					"AND IF(:handle != '' AND :handle is not null, t1.object_handle like CONCAT('%',:handle,'%'), 1=1 ) " +
					"AND IF(:status != '' AND :status is not null, t2.channel_status = :status, 1=1 ) " +
					"AND IF(:startTime IS NOT NULL AND :endTime IS NOT NULL, t2.created_time BETWEEN :startTime AND :endTime , 1=1) " +
					"AND IF(:appHandle != '' AND :appHandle is not null, t1.app_handle_code like CONCAT('%',:appHandle,'%'), 1=1 ) ",
			countQuery = "SELECT count(1) " +
					"FROM t_share_channel t1 " +
					"INNER JOIN t_share_channel_applications t2 ON t1.share_channel_id = t2.share_channel_id " +
					"AND t1.main_version = t2.main_version AND t1.minor_version = t2.minor_version " +
					"LEFT JOIN t_data_channel t3 ON t1.data_channel_id = t3.id " +
					"WHERE t1.is_deleted = 0 and t2.is_deleted = 0 " +
					"AND IF(:channelName != '' AND :channelName is not null, t3.data_channel_name like CONCAT('%',:channelName,'%'), 1=1 ) " +
					"AND IF(:handle != '' AND :handle is not null, t1.object_handle like CONCAT('%',:handle,'%'), 1=1 ) " +
					"AND IF(:status != '' AND :status is not null, t2.channel_status = :status, 1=1 ) " +
					"AND IF(:startTime IS NOT NULL AND :endTime IS NOT NULL, t2.created_time BETWEEN :startTime AND :endTime , 1=1) " +
					"AND IF(:appHandle != '' AND :appHandle is not null, t1.app_handle_code like CONCAT('%',:appHandle,'%'), 1=1 ) "
	)
	Page<ShareChannelsApplicationsView> listShareChannelsApplications(@Param("channelName") String channelName,
																	  @Param("handle") String handle,
																	  @Param("status") String status,
																	  @Param("startTime") LocalDateTime startTime,
																	  @Param("endTime") LocalDateTime endTime,
																	  @Param("appHandle") String appHandle,
																	  Pageable pageable);

	@Query(nativeQuery = true, value = "select " +
			"a.id as id, " +
			"a.share_channel_id as shareChannelId, " +
			"d.id as dataChannelId, " +
			"d.data_channel_name as shareChannelName, " +
			"a.change_reason as changeReason, " +
			"a.object_handle as handle, " +
			"d.data_type as dataType, " +
			"a.main_version as mainVersion, " +
			"a.minor_version as minorVersion, " +
			"b.app_name as appName, " +
			"c.org_name as entName, " +
			"a.status as status, " +
			"a.auto_sql as autoSql, " +
			"a.edit_sql as editSql, " +
			"f.name as applyUser, " +
			"e.created_time as updatedTime " +
			"from t_share_channel a " +
			"left join t_app_info b on a.app_handle_code = b.handle_code " +
			"left join t_ent_prefix c on b.ent_prefix = c.ent_prefix " +
			"left join t_data_channel d on a.data_channel_id = d.id " +
			"inner join t_share_channel_applications e on a.share_channel_id = e.share_channel_id " +
			"and a.main_version = e.main_version and a.minor_version = e.minor_version " +
			"left join sys_user f on e.apply_user_id = f.user_id " +
			"where e.id = :applicationId and e.is_deleted = 0 " +
			"and a.is_deleted = 0 ")
	ShareChannelsApplicationsDetailView queryShareChannelsApplicationDetails(@Param("applicationId") String applicationId);

	List<ShareChannelEntity> findByDataChannelId(Long dataChannelId);

	ShareChannelEntity findByShareChannelIdAndEnable(Long shareChannelId, Integer i);

	@Query(nativeQuery = true,value = "select * from t_share_channel  " +
            "where share_channel_id = :shareChannelId and is_deleted = 0 " +
            "order by main_version desc ,minor_version desc limit  1")
	ShareChannelEntity findMaxVersionByShareChannelId(Long shareChannelId);

	@Modifying
	@Query("UPDATE ShareChannelEntity " +
			"SET enable = 0 " +
			"WHERE shareChannelId = :shareChannelId " +
			"AND enable = 1")
	void updateDisEnableByShareChannelId(@Param("shareChannelId") String shareChannelId);

	@Modifying
	@Query("UPDATE ShareChannelEntity " +
			"SET enable = 1, updateTime = now() " +
			"WHERE shareChannelId = :shareChannelId " +
			"AND mainVersion = :mainVersion " +
			"AND minorVersion = :minorVersion ")
	void updateEnableByShareChannelIdAndVersion(@Param("shareChannelId") String shareChannelId,
												@Param("mainVersion") Integer mainVersion,
												@Param("minorVersion") Integer minorVersion);

	@Query(nativeQuery = true, value = "select " +
			"CONCAT('V', main_version, '.', minor_version) as version " +
			"from t_share_channel " +
			"where share_channel_id = :shareChannelId and is_deleted = 0 " +
			"order by main_version desc, minor_version desc ")
	List<String> queryChannelVersions(@Param("shareChannelId") String shareChannelId);

	ShareChannelEntity findByShareChannelIdAndMainVersionAndMinorVersion(Long shareChannelId, Integer mainVersion, Integer minorVersion);
	@Query(nativeQuery = true,value = """
			SELECT *
                          FROM t_share_channel t1
                          WHERE t1.enable = 1
                            AND t1.is_deleted = 0
                            AND NOT EXISTS (
                              SELECT 1
                              FROM t_share_channel t2
                              WHERE t2.share_channel_id = t1.share_channel_id
                                AND t2.enable = 1
                                AND t2.is_deleted = 0
                                AND (
                                  t2.main_version > t1.main_version
                                      OR (t2.main_version = t1.main_version AND t2.minor_version > t1.minor_version)
                                  )
                          )
                          UNION ALL
                          SELECT *
                          FROM t_share_channel t1
                          WHERE t1.is_deleted = 0
                            AND t1.enable <> 1
                            AND NOT EXISTS (
                              SELECT 1
                              FROM t_share_channel t2
                              WHERE t2.share_channel_id = t1.share_channel_id
                                AND t2.is_deleted = 0
                                AND t2.enable <> 1
                                AND (
                                  t2.main_version > t1.main_version
                                      OR (t2.main_version = t1.main_version AND t2.minor_version > t1.minor_version)
                                  )
                          )
                            AND NOT EXISTS (
                              SELECT 1
                              FROM t_share_channel t3
                              WHERE t3.share_channel_id = t1.share_channel_id
                                AND t3.enable = 1
                                AND t3.is_deleted = 0
                          );
			""")
	List<ShareChannelEntity> findDetectChannel();

	List<ShareChannelEntity> findAllByShareChannelIdAndStatus(Long shareChannelId, Integer status);
}