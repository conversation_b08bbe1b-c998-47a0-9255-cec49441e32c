package cn.teleinfo.ds.business.interfaces.rest;

import cn.teleinfo.ds.business.application.query.ListAppInfoQuery;
import cn.teleinfo.ds.business.application.service.AppInfoService;
import cn.teleinfo.ds.business.interfaces.assembler.AppInfoAssembler;
import cn.teleinfo.ds.business.interfaces.dto.request.ListHandleSignRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleSignAppInfoResponse;
import com.pig4cloud.pig.common.core.util.PageResponse;
import com.pig4cloud.pig.common.core.util.R;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 应用信息
 */
@RestController
@RequestMapping("/handle-sign")
@AllArgsConstructor
public class AppInfoController {

	private final AppInfoService appInfoService;

	private final AppInfoAssembler appInfoAssembler;

	/**
	 * 应用信息列表查询
	 * @param request 过滤条件
	 */
	@GetMapping("/appinfo")
	public R<PageResponse<HandleSignAppInfoResponse>> listHandleSignAppInfo(@Valid ListHandleSignRequest request) {
		ListAppInfoQuery listAppInfoQuery = appInfoAssembler.toListAppInfoQuery(request);
		var handleSignAppInfoDTOPageList = appInfoService.listHandleSignAppInfo(listAppInfoQuery);
		return R.ok(appInfoAssembler.toHandleSignAppInfoResponse(handleSignAppInfoDTOPageList));
	}

	/**
	 * 应用信息详情
	 * @param id id
	 */
	@GetMapping("/appinfo/{id}")
	public R<HandleSignAppInfoResponse> queryHandleSignAppInfoDetail(@PathVariable("id") String id) {
		var handleSignAppInfoDTO = appInfoService.queryHandleSignAppInfoDetail(id);
		return R.ok(appInfoAssembler.toHandleSignAppInfoResponse(handleSignAppInfoDTO));
	}

}
