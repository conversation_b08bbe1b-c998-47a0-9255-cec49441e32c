package cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection;

import cn.teleinfo.ds.business.domain.model.valueobject.ShareChannelAuthValue;

import java.time.LocalDateTime;
import java.util.List;

public interface ShareChannelsApplicationsDetailView {

	Long getId();

	Long getShareChannelId();

	Long getDataChannelId();

	String getShareChannelName();

	String getChangeReason();

	String getHandle();

	Integer getDataType();

	Integer getMainVersion();

	Integer getMinorVersion();

	String getAppName();

	String getEntName();

	Integer getStatus();

	String getAutoSql();

	String getEditSql();

	String getApplyUser();

	LocalDateTime getUpdatedTime();

}
