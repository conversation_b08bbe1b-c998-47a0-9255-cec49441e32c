package cn.teleinfo.ds.business.domain.repository;


import cn.teleinfo.ds.business.infrastructure.persistence.dto.sync.SyncAppDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.dto.sync.SyncChannelDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.dto.sync.SyncHandleDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.dto.sync.SyncUserDTO;

import java.time.LocalDateTime;
import java.util.List;

public interface DataSyncRepository {
//	List<SyncUserDTO> integratedUsers();

	List<SyncChannelDTO> integratedDataChannels();

	List<SyncAppDTO> integratedApplications();

	List<SyncHandleDTO> integratedHandles(Integer page, Integer size, LocalDateTime updatedTime);



}
