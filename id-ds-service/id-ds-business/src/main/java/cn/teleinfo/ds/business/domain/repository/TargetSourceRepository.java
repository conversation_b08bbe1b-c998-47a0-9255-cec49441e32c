package cn.teleinfo.ds.business.domain.repository;

import cn.teleinfo.ds.business.domain.model.aggregate.TargetSource;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceDomainEntity;
import com.pig4cloud.pig.common.core.util.PageResponse;

import java.util.Date;

public interface TargetSourceRepository {

	void save(TargetSourceDomainEntity targetSourceDomainEntity);

	/**
	 * 查询应用下目标源名称是否存在
	 *
	 * @param appHandleCode    app handle code
	 * @param targetSourceName 目标源名称
	 * @return > 0 存在
	 */
	Integer findByAppHandleCodeAndTargetSourceNameCount(String appHandleCode, String targetSourceName);

	PageResponse<TargetSourceDomainEntity> listTargetSource(String appHandleCode,
															String targetSourceName,
															Integer platformType,
															Date start,
															Date end,
															Integer page,
															Integer size
	);

	TargetSourceDomainEntity findById(Long id);
}
