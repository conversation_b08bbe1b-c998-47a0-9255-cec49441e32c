package cn.teleinfo.ds.business.domain.service.impl;

import cn.teleinfo.ds.business.domain.model.aggregate.SysConnection;
import cn.teleinfo.ds.business.domain.repository.HcsRepository;
import cn.teleinfo.ds.business.domain.service.HcsDomainService;
import com.huaweicloud.sdk.cdm.v1.model.Clusters;
import com.huaweicloud.sdk.cdm.v1.model.Job;
import com.huaweicloud.sdk.cdm.v1.model.Links;
import com.huaweicloud.sdk.dataartsstudio.v1.model.*;
import com.huaweicloud.sdk.iam.v3.model.AuthProjectResult;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@AllArgsConstructor
@Service
public class HcsDomainServiceImpl implements HcsDomainService {
	private final HcsRepository hcsRepository;

	/**
	 * 数据集成-规范层连接名称
	 */
	@Override
	public List<Links> findCdmConnections(SysConnection sysConnection, String projectId, String clusterId) {
		sysConnection.check();

		var ak = sysConnection.getHcsConnContent().getAk();
		var sk = sysConnection.getHcsConnContent().getSk();
		var endpoints = sysConnection.getHcsConnContent().getCdmEndpoint();
		return hcsRepository.findCdmConnections(ak, sk, projectId, List.of(endpoints), clusterId);
	}

	/**
	 * 数据开发-规范层连接名称
	 */
	@Override
	public List<ApigDataSourceView> findDayuConnections(SysConnection sysConnection, String projectId,
														String workspace, Integer offset, Integer limit) {
		sysConnection.check();
		var ak = sysConnection.getHcsConnContent().getAk();
		var sk = sysConnection.getHcsConnContent().getSk();
		var endpoints = sysConnection.getHcsConnContent().getDgcEndpoint();
		return hcsRepository.findDayuConnections(ak, sk, projectId, List.of(endpoints), workspace, offset, limit);
	}

	/**
	 * 数据开发-规范层数据库名称
	 */
	@Override
	public List<DatabasesList> findDayuConnectionsDatabases(SysConnection sysConnection, String projectId,
															String workspace, String connectionId, Integer offset, Integer limit) {
		sysConnection.check();
		var ak = sysConnection.getHcsConnContent().getAk();
		var sk = sysConnection.getHcsConnContent().getSk();
		var endpoints = sysConnection.getHcsConnContent().getDgcEndpoint();
		return hcsRepository.findDayuConnectionsDatabases(ak, sk, projectId, List.of(endpoints), workspace, connectionId, offset, limit);
	}

	/**
	 * 获取资源空间列表
	 */
	@Override
	public List<AuthProjectResult> findProjects(SysConnection sysConnection) {
		sysConnection.check();
		var ak = sysConnection.getHcsConnContent().getAk();
		var sk = sysConnection.getHcsConnContent().getSk();
		var endpoints = sysConnection.getHcsConnContent().getIamEndpoint();
		return hcsRepository.findProjects(ak, sk, List.of(endpoints));
	}

	/**
	 * 获取数据治理中心实例列表
	 */
	@Override
	public List<ApigCommodityOrder> findDasInstances(SysConnection sysConnection, String projectId, Integer page, Integer size) {
		sysConnection.check();
		var ak = sysConnection.getHcsConnContent().getAk();
		var sk = sysConnection.getHcsConnContent().getSk();
		var endpoints = sysConnection.getHcsConnContent().getDgcEndpoint();
		return hcsRepository.findDasInstances(ak, sk, projectId, List.of(endpoints), page, size);
	}

	/**
	 * 获取工作空间列表
	 */
	@Override
	public List<Workspacebody> findDasWorkspaces(SysConnection sysConnection, String projectId, String instanceId, Integer page, Integer size) {
		sysConnection.check();
		var ak = sysConnection.getHcsConnContent().getAk();
		var sk = sysConnection.getHcsConnContent().getSk();
		var endpoints = sysConnection.getHcsConnContent().getDgcEndpoint();
		return hcsRepository.findDasWorkspaces(ak, sk, projectId, instanceId, List.of(endpoints), page, size);
	}

	/**
	 * 获取CDM集群名称列表
	 */
	@Override
	public List<Clusters> findCdmClusters(SysConnection sysConnection, String projectId) {
		sysConnection.check();
		var ak = sysConnection.getHcsConnContent().getAk();
		var sk = sysConnection.getHcsConnContent().getSk();
		var endpoints = sysConnection.getHcsConnContent().getCdmEndpoint();
		return hcsRepository.findCdmClusters(ak, sk, projectId, List.of(endpoints));
	}

	@Override
	public List<Job> findJobs(SysConnection sysConnection, String projectId, String clusterId) {
		sysConnection.check();
		var ak = sysConnection.getHcsConnContent().getAk();
		var sk = sysConnection.getHcsConnContent().getSk();
		var endpoints = sysConnection.getHcsConnContent().getCdmEndpoint();
		return hcsRepository.findJobs(ak,sk,projectId,List.of(endpoints),clusterId);
	}

	@Override
	public void createScript(SysConnection sysConnection, String projectId, String scriptName, String scriptContent, String workspace,String databaseName,String connectionName) {

		sysConnection.check();
		var ak = sysConnection.getHcsConnContent().getAk();
		var sk = sysConnection.getHcsConnContent().getSk();
		var endpoints = sysConnection.getHcsConnContent().getDgcEndpoint();
		hcsRepository.createScript(ak,sk,projectId,List.of(endpoints),scriptName,scriptContent,workspace,databaseName,connectionName);
	}

	@Override
	public String executeScript(SysConnection sysConnection, String projectId,String scriptName, String workspace) {
		sysConnection.check();
		var ak = sysConnection.getHcsConnContent().getAk();
		var sk = sysConnection.getHcsConnContent().getSk();
		var endpoints = sysConnection.getHcsConnContent().getDgcEndpoint();
	return 	hcsRepository.executeScript(ak,sk,projectId,List.of(endpoints),scriptName,workspace);

	}

	@Override
	public Map<String, String> listScriptResults(SysConnection sysConnection, String projectId,String scriptName, String workspace, String instanceId) {
		sysConnection.check();
		var ak = sysConnection.getHcsConnContent().getAk();
		var sk = sysConnection.getHcsConnContent().getSk();
		var endpoints = sysConnection.getHcsConnContent().getCdmEndpoint();
		return hcsRepository.listScriptResults(ak,sk,projectId,List.of(endpoints),scriptName,workspace,instanceId);
	}

	@Override
	public List<TablesList> listTables(SysConnection sysConnection,String projectId, String connectionId, String databaseName, String tableName) {
		sysConnection.check();
		var ak = sysConnection.getHcsConnContent().getAk();
		var sk = sysConnection.getHcsConnContent().getSk();
		var endpoints = sysConnection.getHcsConnContent().getDgcEndpoint();
		return hcsRepository.listTables(ak,sk,projectId,List.of(endpoints),connectionId,databaseName,tableName);

	}

	@Override
	public List<ColumnsList> listTableColumns(SysConnection sysConnection,String projectId, String connectionId, String workSpace, String tableId) {
		sysConnection.check();
		var ak = sysConnection.getHcsConnContent().getAk();
		var sk = sysConnection.getHcsConnContent().getSk();
		var endpoints = sysConnection.getHcsConnContent().getCdmEndpoint();
		return hcsRepository.listTableColumns(ak,sk,projectId,List.of(endpoints),connectionId,workSpace,tableId);
	}
}
