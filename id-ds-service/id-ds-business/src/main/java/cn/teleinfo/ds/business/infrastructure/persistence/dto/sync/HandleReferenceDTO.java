package cn.teleinfo.ds.business.infrastructure.persistence.dto.sync;


import lombok.Data;

import java.time.LocalDateTime;

@Data
public class HandleReferenceDTO {

	private String appHandleCode;
	private LocalDateTime createdTime;
	private String entPrefix;
	private Long handleItemId;
	private Long id;
	private Integer isDeleted;
	private String paramProp;
	private Long paramPropIndex;
	private String provincePrefix;
	private String queryProp;
	private Long queryPropIndex;
	private String referenceHandle;
	private String referenceHandleProp;
	private Long referenceHandlePropIndex;
	private LocalDateTime updatedTime;
}
