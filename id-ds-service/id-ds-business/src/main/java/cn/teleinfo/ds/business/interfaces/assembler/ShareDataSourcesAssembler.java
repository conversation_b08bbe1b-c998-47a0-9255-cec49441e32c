package cn.teleinfo.ds.business.interfaces.assembler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.application.command.CreateShareDataSourcesCommand;
import cn.teleinfo.ds.business.application.query.ListShareDataSourcesQuery;
import cn.teleinfo.ds.business.interfaces.dto.request.CreateShareDataSourcesRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.ListShareDataSourcesRequest;
import org.springframework.stereotype.Component;

@Component
public class ShareDataSourcesAssembler {
	public CreateShareDataSourcesCommand toCreateShareDataSourcesCommand(CreateShareDataSourcesRequest request) {
		CreateShareDataSourcesCommand command = new CreateShareDataSourcesCommand();
		command.setPlatformType(request.getPlatformType());
		command.setAppHandleCode(request.getAppHandleCode());

		if (!JSONUtil.isTypeJSON(request.getItems())) {
			throw new IllegalArgumentException("共享源信息格式错误");
		}

		command.setItems(request.getItems());
		return command;
	}

	public ListShareDataSourcesQuery toListShareDataSourcesQuery(ListShareDataSourcesRequest request) {
		return BeanUtil.toBean(request, ListShareDataSourcesQuery.class);
	}
}
