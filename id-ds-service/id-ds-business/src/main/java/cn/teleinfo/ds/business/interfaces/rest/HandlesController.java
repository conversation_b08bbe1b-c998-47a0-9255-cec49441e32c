package cn.teleinfo.ds.business.interfaces.rest;

import cn.teleinfo.ds.business.application.query.ListHandlesQuery;
import cn.teleinfo.ds.business.application.service.HandlesApplicationService;
import cn.teleinfo.ds.business.domain.model.aggregate.Handle;
import cn.teleinfo.ds.business.interfaces.assembler.HandlesAssembler;
import cn.teleinfo.ds.business.interfaces.dto.request.ListHandlesRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.HandInfoResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleDirectoryResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.ListHandlesResponse;
import com.pig4cloud.pig.common.core.util.PageResponse;
import com.pig4cloud.pig.common.core.util.R;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

// 标识地图
@RestController
@RequestMapping("/handles")
@AllArgsConstructor
public class HandlesController {

	private final HandlesApplicationService handlesApplicationService;
	private final HandlesAssembler handlesAssembler;

	/**
	 * 对象标识列表
	 */
	@GetMapping
	public R<PageResponse<ListHandlesResponse>> listHandles(@Valid ListHandlesRequest request) {
		ListHandlesQuery query = handlesAssembler.toListHandlesQuery(request);
		PageResponse<Handle> response = handlesApplicationService.listHandles(query);
		return R.ok(handlesAssembler.toListHandles(response));
	}

	/**
	 * 对象标识详情
	 */
	@GetMapping("/{handle}")
	public R<HandInfoResponse> info(@PathVariable("handle") String handle) {
		return R.ok(handlesAssembler.toHandInfoResponse(handlesApplicationService.findByHandle(handle)));
	}


	@GetMapping("/directory")
	public R<List<HandleDirectoryResponse>> directory(){
		return R.ok(handlesAssembler.toHandleDirectoryResponse(handlesApplicationService.directory()));
	}
}
