package cn.teleinfo.ds.business;

import com.pig4cloud.pig.common.feign.annotation.EnablePigFeignClients;
import com.pig4cloud.pig.common.security.annotation.EnablePigResourceServer;
import cn.teleinfo.ds.common.swagger.annotation.EnableDoc;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

/**
 * <AUTHOR>
 * @date 2018年06月21日
 * <p>
 * 用户统一管理系统
 */
@EnableDoc(value = "business")
@EnablePigFeignClients(basePackages = {"cn.teleinfo.ds.*"})
@EnablePigResourceServer
@EnableDiscoveryClient
@SpringBootApplication
@EnableJpaAuditing(auditorAwareRef = "auditorConfig")
public class DsBusinessApplication {

	public static void main(String[] args) {
		SpringApplication.run(DsBusinessApplication.class, args);
	}

}
