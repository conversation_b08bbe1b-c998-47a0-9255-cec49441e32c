package cn.teleinfo.ds.business.application.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.application.command.CreateTargetSourceCommand;
import cn.teleinfo.ds.business.application.query.ListTargetSourceQuery;
import cn.teleinfo.ds.business.application.service.TargetSourceApplicationService;
import cn.teleinfo.ds.business.domain.model.aggregate.TargetSource;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceDomainEntity;
import cn.teleinfo.ds.business.domain.model.valueobject.AppHandleCode;
import cn.teleinfo.ds.business.domain.service.TargetSourceDomainService;
import com.pig4cloud.pig.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;


@Service
@AllArgsConstructor
public class TargetSourceApplicationServiceImpl implements TargetSourceApplicationService {
	private final TargetSourceDomainService targetSourceDomainService;


	/**
	 * 创建目标源
	 *
	 * @param command command
	 */
	@Override
	public void createTargetSource(CreateTargetSourceCommand command) {
		TargetSourceDomainEntity entity = BeanUtil.copyProperties(command, TargetSourceDomainEntity.class);
		TargetSource targetSource = new TargetSource(entity, new AppHandleCode(command.getAppHandleCode()));
		targetSourceDomainService.save(targetSource);
	}

	/**
	 * 查询目标源
	 *
	 * @param query query
	 */
	@Override
	public PageResponse<TargetSourceDomainEntity> listTargetSource(ListTargetSourceQuery query) {
		TargetSourceDomainEntity entity = BeanUtil.copyProperties(query, TargetSourceDomainEntity.class);
		TargetSource targetSource = new TargetSource(entity);
		return targetSourceDomainService.listTargetSource(targetSource, query.getStart(), query.getEnd(), query.getPage(), query.getSize());
	}
}
