package cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ShareChannelsDTO {

	/**
	 * id
	 */
	private Long id;

	/**
	 * 共享通道id
	 */
	private Long shareChannelId;

	/**
	 * 数据通道id
	 */
	private Long dataChannelId;

	/**
	 * 共享通道名称
	 */
	private String shareChannelName;

	/**
	 * 所属标识
	 */
	private String handle;

	/**
	 * 实例数据类型
	 */
	private Integer dataType;

	/**
	 * 主版本号
	 */
	private Integer mainVersion;

	/**
	 * 次版本号
	 */
	private Integer minorVersion;

	/**
	 * 操作时间
	 */
	private Timestamp updatedTime;

	/**
	 * 探测状态
	 */
	private Integer status;

}
