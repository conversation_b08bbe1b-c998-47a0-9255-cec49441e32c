package cn.teleinfo.ds.business.interfaces.dto.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ShareChannelsVersionResponse {

	/**
	 * id
	 */
	private String id;

	/**
	 * 共享通道id
	 */
	private String shareChannelId;

	/**
	 * 数据通道id
	 */
	private String dataChannelId;

	/**
	 * 共享通道名称
	 */
	private String shareChannelName;

	/**
	 * 所属标识
	 */
	private String handle;

	/**
	 * 数据类型
	 */
	private String dataType;

	/**
	 * 主版本号
	 */
	private Integer mainVersion;

	/**
	 * 次版本号
	 */
	private Integer minorVersion;

	/**
	 * 应用名
	 */
	private String appName;

	/**
	 * 企业名
	 */
	private String entName;

	/**
	 * 探测状态
	 */
	private String status;

	/**
	 * 自动sql
	 */
	private String autoSql;

	/**
	 * 手动sql
	 */
	private String editSql;

	/**
	 * 变更原因
	 */
	private String changeReason;

	/**
	 * 启用状态
	 */
	private Integer enable;

	/**
	 * 任务状态
	 */
	private Integer channelStatus;

	/**
	 * 修改人
	 */
	private String editorUser;

	/**
	 * 修改时间
	 */
	private Timestamp updatedTime;

	/**
	 * 审核记录
	 */
	private List<ShareChannelAuthResponse> items;

}
