package cn.teleinfo.ds.business.domain.repository;

import cn.teleinfo.ds.business.application.query.SharedTaskInstanceListQuery;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskInstanceDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskInstanceListView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskInstanceView;
import com.pig4cloud.pig.common.core.util.PageResponse;

public interface SharedTaskInstanceRepository {

	PageResponse<SharedTaskInstanceListView> listSharedTaskInstances(SharedTaskInstanceListQuery query, Integer page, Integer size);

	SharedTaskInstanceView getSharedTaskInstanceById(Long instanceId);


}
