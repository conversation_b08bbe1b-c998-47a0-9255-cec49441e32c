package cn.teleinfo.ds.business.application.service.impl;

import cn.teleinfo.ds.business.application.query.ListHandlesQuery;
import cn.teleinfo.ds.business.application.service.HandlesApplicationService;
import cn.teleinfo.ds.business.domain.model.aggregate.Handle;
import cn.teleinfo.ds.business.domain.model.aggregate.HandleDirectory;
import cn.teleinfo.ds.business.domain.model.entity.HandleDomainEntity;
import cn.teleinfo.ds.business.domain.service.HandleDirectoryDomainService;
import cn.teleinfo.ds.business.domain.service.HandlesDomainService;
import com.pig4cloud.pig.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class HandlesApplicationServiceImpl implements HandlesApplicationService {
	private final HandlesDomainService handlesDomainService;
	private final HandleDirectoryDomainService handleDirectoryDomainService;

	@Override
	public PageResponse<Handle> listHandles(ListHandlesQuery query) {
		Handle handle = new Handle(new HandleDomainEntity(query.getName(), query.getAppHandleCode(), query.getHandle()));
		return handlesDomainService.listHandles(handle, query.getPage(), query.getSize());
	}

	@Override
	public HandleDomainEntity findByHandle(String handle) {
		return handlesDomainService.findByHandle(handle);
	}

	@Override
	public List<HandleDirectory> directory() {
		return handleDirectoryDomainService.findHandleDirectory();
	}
}
