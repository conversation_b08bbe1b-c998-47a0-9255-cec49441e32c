package cn.teleinfo.ds.business.application.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.application.query.ListSharedTaskQuery;
import cn.teleinfo.ds.business.application.service.SharedTaskApplicationService;
import cn.teleinfo.ds.business.domain.model.aggregate.Handle;
import cn.teleinfo.ds.business.domain.model.aggregate.SharedTask;
import cn.teleinfo.ds.business.domain.model.entity.AppInfoDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleItemDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareChannel;
import cn.teleinfo.ds.business.domain.model.entity.ShareDataSourcesDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDetail;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.SysConnectionDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceDomainEntity;
import cn.teleinfo.ds.business.domain.service.AppInfoDomainService;
import cn.teleinfo.ds.business.domain.service.HandlesDomainService;
import cn.teleinfo.ds.business.domain.service.ShareChannelsDomainService;
import cn.teleinfo.ds.business.domain.service.ShareDataSourcesDomainService;
import cn.teleinfo.ds.business.domain.service.SharedTaskDomainService;
import cn.teleinfo.ds.business.domain.service.SysConnectionDomainService;
import cn.teleinfo.ds.business.domain.service.TargetSourceDomainService;
import cn.teleinfo.ds.business.infrastructure.external.hcs.HcsClient;
import com.pig4cloud.pig.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class SharedTaskApplicationServiceImpl implements SharedTaskApplicationService {
	private final SharedTaskDomainService sharedTaskDomainService;
	private final TargetSourceDomainService targetSourceDomainService;
	private final HandlesDomainService handlesDomainService;
	private final ShareDataSourcesDomainService shareDataSourcesDomainService;
	private final SysConnectionDomainService sysConnectionDomainService;
	private final AppInfoDomainService appInfoDomainService;
	private final ShareChannelsDomainService shareChannelsDomainService;

	@Override
	public PageResponse<SharedTaskDomainEntity> listSharedTask(ListSharedTaskQuery query) {
		SharedTaskDomainEntity entity = BeanUtil.copyProperties(query, SharedTaskDomainEntity.class);
		return sharedTaskDomainService.listSharedTask(entity, query.getStart(), query.getEnd(), query.getPage(), query.getSize());
	}

	@Override
	public SharedTaskDomainEntity getSharedTask(Long id) {
		return sharedTaskDomainService.getSharedTask(id);
	}

	/**
	 * 执行共享任务
	 *
	 * @param id 任务 id
	 */
	@Override
	public void execute(Long id) {
		// 查询任务
		SharedTaskDomainEntity task = sharedTaskDomainService.getSharedTask(id);

		// 查询对象标识
		List<HandleDomainEntity> handles = new ArrayList<>();
		List<SharedTaskDetail> details = task.getDetails();
		for (SharedTaskDetail detail : details) {
			HandleDomainEntity handle = handlesDomainService.findByHandle(detail.getHandle());

			for (HandleItemDomainEntity item : handle.getHandleItems()) {
				ShareChannel enableChannel = shareChannelsDomainService.findEnableChannel(item.getDataChannelId());
				item.setShareChannel(enableChannel);
			}

			handles.add(handle);
		}


		// 查询应用
		AppInfoDomainEntity appInfo = appInfoDomainService.findByHandleCode(task.getAppHandleCode());

		// 查询共享源
		ShareDataSourcesDomainEntity shareDataSource = shareDataSourcesDomainService.findByAppHandleCode(task.getAppHandleCode());
		appInfo.setShareDataSources(shareDataSource);

		// 目标源
		TargetSourceDomainEntity targetSource = targetSourceDomainService.findById(task.getTargetSourceId());

		// 系统连接
		SysConnectionDomainEntity conn = sysConnectionDomainService.findByPlatformType(targetSource.getPlatformType());

		SharedTask sharedTask = new SharedTask(task, handles, appInfo, targetSource, conn);


		// 创建输出表
		sharedTaskDomainService.createOutputTables(sharedTask);

	}
}
