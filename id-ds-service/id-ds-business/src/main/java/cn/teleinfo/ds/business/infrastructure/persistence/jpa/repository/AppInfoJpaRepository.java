package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.HandleSignAppInfoDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.AppInfoEntity;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface AppInfoJpaRepository extends BaseRepository<AppInfoEntity, Long> {


	List<AppInfoEntity> findByEntPrefix(String entPrefix);

	AppInfoEntity findByHandleCode(String appHandleCode);

	@Query(nativeQuery = true,
			value = "SELECT " +
					"a.id as id, " +
					"a.app_name as appName, " +
					"b.org_name as entName, " +
					"SUBSTRING_INDEX(a.handle_code, '/', 1) as handlePrefix, " +
					"a.handle_code as handleCode, " +
					"a.deploy_address as deployAddress, " +
					"a.sys_version as sysVersion, " +
					"a.update_time as updatedTime " +
					"FROM t_app_info a " +
					"LEFT JOIN t_ent_prefix b ON a.ent_prefix = b.ent_prefix " +
					"WHERE a.is_deleted = 0 " +
					"AND IF(:appName != '' AND :appName is not null, a.app_name like CONCAT('%',:appName,'%'), 1=1 ) " +
					"AND IF(:handleCode != '' AND :handleCode is not null, a.handle_code like CONCAT('%',:handleCode,'%'), 1=1 )",
			countQuery = "SELECT count(1) " +
					"FROM t_app_info a " +
					"LEFT JOIN t_ent_prefix b ON a.ent_prefix = b.ent_prefix " +
					"WHERE a.is_deleted = 0 " +
					"AND IF(:appName != '' AND :appName is not null, a.app_name like CONCAT('%',:appName,'%'), 1=1 ) " +
					"AND IF(:handleCode != '' AND :handleCode is not null, a.handle_code like CONCAT('%',:handleCode,'%'), 1=1 )"
	)
	Page<HandleSignAppInfoDTO> listHandleSignAppInfo(@Param("appName") String appName, @Param("handleCode") String handleCode, Pageable pageable);

	@Query(nativeQuery = true,
			value = "SELECT " +
					"a.id as id, " +
					"a.app_name as appName, " +
					"b.org_name as entName, " +
					"SUBSTRING_INDEX(a.handle_code, '/', 1) as handlePrefix, " +
					"a.handle_code as handleCode, " +
					"a.deploy_address as deployAddress, " +
					"a.sys_version as sysVersion, " +
					"a.update_time as updatedTime " +
					"FROM t_app_info a " +
					"LEFT JOIN t_ent_prefix b ON a.ent_prefix = b.ent_prefix " +
					"WHERE a.is_deleted = 0 AND a.id = :id ")
	HandleSignAppInfoDTO queryHandleSignAppInfoDetail(@Param("id") String id);
}