package cn.teleinfo.ds.business.domain.service.impl;

import cn.teleinfo.ds.business.domain.model.entity.SysConnectionDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.SysConnectionPlatformType;
import cn.teleinfo.ds.business.domain.repository.SysConnectionRepository;
import cn.teleinfo.ds.business.domain.service.SysConnectionDomainService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@AllArgsConstructor
@Service
public class SysConnectionDomainServiceImpl implements SysConnectionDomainService {
	private final SysConnectionRepository sysConnectionRepository;

	@Override
	public SysConnectionDomainEntity findByPlatformType(Integer platformType) {
		var code = SysConnectionPlatformType.findByCode(platformType);
		return sysConnectionRepository.findByPlatformType(code);
	}

	@Override
	public void updateConnectionSetting(Integer platformType, String platformConnection) {
		sysConnectionRepository.updateConnectionSetting(platformType, platformConnection);
	}
}
