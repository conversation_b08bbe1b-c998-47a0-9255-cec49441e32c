package cn.teleinfo.ds.business.application.query;

import com.pig4cloud.pig.common.core.util.PageRequest;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ListSharedTaskQuery extends PageRequest {
	/**
	 * 任务名称
	 */
	private String taskName;
	/**
	 * 任务类型：1-手动任务，2-定时任务
	 */
	private Integer taskType;
	/**
	 * 运行状态：1-运行中，2-成功，3-失败，0-未运行
	 */
	private Integer runStatus;
	/**
	 * 最后一次运行时间-开始时间
	 */
	private LocalDateTime start;
	/**
	 * 最后一次运行时间-结束时间
	 */
	private LocalDateTime end;
}
