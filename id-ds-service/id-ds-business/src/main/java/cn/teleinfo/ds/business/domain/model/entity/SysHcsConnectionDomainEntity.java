package cn.teleinfo.ds.business.domain.model.entity;


import lombok.Getter;
import lombok.Setter;

/**
 * 华为连接配置管理
 */
@Getter
@Setter
public class SysHcsConnectionDomainEntity {
	/**
	 * IAM_ENDPOINT
	 */
	private String iamEndpoint;
	/**
	 * CDM_ENDPOINT
	 */
	private String cdmEndpoint;
	/**
	 * Dgc_ENDPOINT
	 */
	private String dgcEndpoint;
	/**
	 * DataArtsStudio_ENDPOINT
	 */
	private String dataArtsStudioEndpoint;
	/**
	 * ak
	 */
	private String ak;
	/**
	 * sk
	 */
	private String sk;

	/**
	 * 数据集成-规范层连接名称
	 */
	private String dataConnName;
	/**
	 * 数据集成-规范层数据库名称
	 */
	private String dataDatabaseName;
	/**
	 * 数据开发-规范层连接名称
	 */
	private String dataDevConnName;
	/**
	 * 数据开发-规范层数据库名称
	 */
	private String dataDevDatabaseName;
}
