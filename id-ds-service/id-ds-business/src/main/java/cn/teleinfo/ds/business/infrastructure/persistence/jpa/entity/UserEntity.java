package cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

/**
 * 用户表
 */
@Getter
@Setter
@Entity
@Table(name = "t_user")
@SQLDelete(sql = "update t_user set is_deleted = null where id = ?")
@SQLRestriction("is_deleted = 0")
public class UserEntity extends BaseEntity {

    /**
     * 用户原始id
     */
    @Column(name = "source_id")
    private Long sourceId;

    /**
     * 应用身份标识
     */
    @Column(name = "app_handle_code")
    private String appHandleCode;

    /**
     * 企业前缀
     */
    @Column(name = "ent_prefix")
    private String entPrefix;

    /**
     * sys_admin,sys_app_user
     */
    @Column(name = "role_code")
    private String roleCode;

    /**
     * 省级前缀
     */
    @Column(name = "province_prefix")
    private String provincePrefix;

    /**
     * 用户名
     */
    @Column(name = "username", length = 32)
    private String username;

    /**
     * 昵称
     */
    @Column(name = "nick_name", length = 64)
    private String nickName;

    /**
     * 密码
     */
    @Column(name = "password")
    private String password;

    /**
     * 地址
     */
    @Column(name = "address")
    private String address;

    /**
     * 电话
     */
    @Column(name = "phone")
    private String phone;

    /**
     * 邮箱
     */
    @Column(name = "email")
    private String email;

    /**
     * 1-省；2-企业
     */
    @Column(name = "source_type")
    private Integer sourceType;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;
} 