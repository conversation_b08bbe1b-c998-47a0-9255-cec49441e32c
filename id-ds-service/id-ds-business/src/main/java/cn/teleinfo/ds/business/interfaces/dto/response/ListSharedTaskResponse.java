package cn.teleinfo.ds.business.interfaces.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ListSharedTaskResponse {
	private Long id;
	/**
	 * 任务编号，如RW202506001
	 */
	private String taskNo;
	/**
	 * 任务名称
	 */
	private String taskName;
	/**
	 * 任务类型：1-手动任务，2-定时任务
	 */
	private Integer taskType;
	/**
	 * 任务状态：1-启用，0-禁用
	 */
	private Integer taskStatus;

	/**
	 * 测试状态：1-成功，2-失败，0-未测试
	 */
	private Integer testStatus;


	/**
	 * 运行状态：1-运行中，2-成功，3-失败，0-未运行
	 */
	private Integer runStatus;

	/**
	 * 最后一次运行时间
	 */
	private LocalDateTime lastRunTime;
}
