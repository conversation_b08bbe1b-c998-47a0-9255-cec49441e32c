package cn.teleinfo.ds.business.domain.model.entity;

import lombok.Data;

// 共享源配置信息
@Data
public class ShareDataSourcesItem {

	/**
	 * 资源空间
	 */
	private String projectId;

	/**
	 * 数据治理中心实例 id
	 */
	private String instanceId;

	/**
	 * 工作空间
	 */
	private String workspace;

	/**
	 * 数据集成-集群
	 */
	private String clusterId;

	/**
	 * 数据集成-贴源层连接名称
	 */
	private String stgConnName;
	/**
	 * 数据集成-贴源层数据库名称
	 */
	private String stgDatabaseName;

	/**
	 * 数据集成-规范层连接名称
	 */
	private String stdDataConnName;
	/**
	 * 数据集成-规范层数据库名称
	 */
	private String stdDataDatabaseName;
	/**
	 * 数据开发-规范层连接名称
	 */
	private String stdDataDayuConnName;
	/**
	 * 数据开发-规范层数据库名称
	 */
	private String stdDataDayuDatabaseName;
}
