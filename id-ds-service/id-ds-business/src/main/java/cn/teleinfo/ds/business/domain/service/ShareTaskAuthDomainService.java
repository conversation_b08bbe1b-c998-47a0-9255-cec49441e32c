package cn.teleinfo.ds.business.domain.service;

import cn.teleinfo.ds.business.application.query.ShareTaskAuthQuery;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskApplicationDetails;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskAuth;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskAuthDomainEntity;
import cn.teleinfo.ds.business.domain.model.valueobject.AuthInfo;
import cn.teleinfo.ds.business.domain.model.valueobject.ShareTaskApplicationsId;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareTaskAuthView;
import cn.teleinfo.ds.business.interfaces.dto.request.ListShareTaskAuthsRequest;
import com.pig4cloud.pig.common.core.util.PageResponse;

/**
 * 共享任务授权
 */
public interface ShareTaskAuthDomainService {

	/**
	 * 共享任务授权
	 *
	 * @param shareTaskAuth 授权聚合根
	 * @param authInfo      审核信息值对象
	 */
	void review(ShareTaskAuth shareTaskAuth, AuthInfo authInfo);

	/**
	 * 查询当前已经授权的记录
	 *
	 * @param applicationsId 共享任务id
	 * @return 授权记录
	 */
	ShareTaskAuthDomainEntity findShareTaskAuthByPass(ShareTaskApplicationsId applicationsId);

	PageResponse<ShareTaskAuthView> listShareTaskAuths(ShareTaskAuthQuery query, ListShareTaskAuthsRequest request);

	/**
	 * 根据共享任务授权id获取共享任务详情
	 * @param shareTaskAuthId 共享任务授权id
	 * @return 共享任务详情
	 */
	ShareTaskApplicationDetails getShareTaskAuthDetail(Long shareTaskAuthId);
}
