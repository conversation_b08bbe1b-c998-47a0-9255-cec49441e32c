package cn.teleinfo.ds.business.domain.model.entity;

import jakarta.persistence.Column;
import lombok.Data;

import java.util.Date;

/**
 * 共享任务授权详情
 * 共享任务信息备份
 */
@Data
public class ShareTaskAuthDetailsDomainEntity {

	/**
	 *  主键ID
	 */
	private Long id;

	/**
	 *  t_share_task_auth
	 */
	private Long shareTaskAuthId;

	/**
	 *  省级前缀
	 */
	private String provincePrefix;

	/**
	 *  企业前缀
	 */
	private String entPrefix;

	/**
	 *  应用身份编码
	 */
	private String appHandleCode;

	/**
	 *  标识编码
	 */
	private String handle;

	/**
	 *  标识属性
	 */
	private String fields;

	/**
	 *  共享任务编号
	 */
	private String taskNo;

	/**
	 *  创建人
	 */
	private Long createBy;

	/**
	 *  修改人
	 */
	private Long updateBy;

	/**
	 *  创建时间
	 */
	private Date createTime;

	/**
	 *  更新时间
	 */
	private Date updateTime;




}
