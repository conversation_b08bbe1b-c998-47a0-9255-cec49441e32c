package cn.teleinfo.ds.business.domain.repository;

import cn.teleinfo.ds.business.application.query.ListAppInfoQuery;
import cn.teleinfo.ds.business.domain.model.entity.AppInfoDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.HandleSignAppInfoDTO;
import com.pig4cloud.pig.common.core.util.PageResponse;

import java.util.List;

public interface AppInfoRepository {

	/**
	 * 查询企业前缀下的应用
	 * @param entPrefix 企业前缀
	 * @return
	 */
	List<AppInfoDomainEntity> findAllByEntPrefix(String entPrefix);

	/**
	 * 应用信息列表查询
	 * @param query 过滤条件
	 */
	PageResponse<HandleSignAppInfoDTO> listHandleSignAppInfo(ListAppInfoQuery query, Integer page, Integer size);

	/**
	 * 应用信息详情
	 * @param id id
	 */
	HandleSignAppInfoDTO queryHandleSignAppInfoDetail(String id);


	AppInfoDomainEntity findByHandleCode(String handleCode);
}
