package cn.teleinfo.ds.business.interfaces.assembler;

import cn.hutool.core.collection.CollectionUtil;
import cn.teleinfo.ds.business.application.command.sharetaskapplications.ShareTaskApplicationsCommand;
import cn.teleinfo.ds.business.application.query.ShareTaskApplicationsQuery;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskApplicationDetails;
import cn.teleinfo.ds.business.domain.model.valueobject.ShareTaskApplicationDetailValue;
import cn.teleinfo.ds.business.domain.model.valueobject.ShareTaskAuthApplicationValue;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareTaskAuthDTO;
import cn.teleinfo.ds.business.interfaces.dto.request.ListShareTaskApplicationsRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.ShareTaskApplicationsRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.ShareTaskApplicationDetailDataResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.ShareTaskApplicationDetailResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.ShareTaskApplicationResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.ShareTaskAuthApplicationResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;

@Component
public class ShareTaskApplicationsAssemble {

	public ShareTaskApplicationsCommand toCommand(ShareTaskApplicationsRequest request) {
		ShareTaskApplicationsCommand command = new ShareTaskApplicationsCommand();
		BeanUtils.copyProperties(request, command);
		return command;
	}

	public ShareTaskApplicationsQuery toQueryList(ListShareTaskApplicationsRequest request) {
		ShareTaskApplicationsQuery query = new ShareTaskApplicationsQuery();
		BeanUtils.copyProperties(request, query);
		return query;
	}

	/**
	 * 转换详情信息
	 * @param details 详情信息
	 * @return 详情信息响应
	 */
	public ShareTaskApplicationDetailResponse toDetailResponse(ShareTaskApplicationDetails details) {
		ShareTaskApplicationDetailResponse query = new ShareTaskApplicationDetailResponse();
		ShareTaskApplicationResponse applicationResponse = new ShareTaskApplicationResponse();
		BeanUtils.copyProperties(details.getBasicInfo(), applicationResponse);
		query.setBasicInfo(applicationResponse);
		// 获取授权信息
		if (CollectionUtil.isNotEmpty(details.getAuthorizeRecords())) {
			ArrayList<ShareTaskAuthApplicationResponse> values = new ArrayList<>();
			for (ShareTaskAuthApplicationValue auth : details.getAuthorizeRecords()) {
				ShareTaskAuthApplicationResponse value = new ShareTaskAuthApplicationResponse();
				BeanUtils.copyProperties(auth, value);
				values.add(value);
			}
			query.setAuthorizeRecords(values);
		}
		// 获取共享数据
		if (CollectionUtil.isNotEmpty(details.getShareData())) {
			ArrayList<ShareTaskApplicationDetailDataResponse> detailValues = new ArrayList<>();
			details.getShareData().forEach(detail -> {
				ShareTaskApplicationDetailDataResponse value = new ShareTaskApplicationDetailDataResponse();
				BeanUtils.copyProperties(detail, value);
				detailValues.add(value);
			});
			query.setShareData(detailValues);
		}
		return query;
	}
}
