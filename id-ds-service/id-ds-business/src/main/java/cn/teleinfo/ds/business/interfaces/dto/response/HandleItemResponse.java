package cn.teleinfo.ds.business.interfaces.dto.response;

import lombok.Data;

@Data
public class HandleItemResponse {
	private Long id;

	/**
	 * 属性字段
	 */
	private String field;
	/**
	 * 属性值（大文本）
	 */
	private String fieldValue;

	/**
	 * 描述
	 */
	private String description;

	/**
	 * 属性类型（1固定值 2标识解析数据源 3标识值 4标识-属性）
	 */
	private Integer fieldType;

	/**
	 * 标识ID（关联t_handle.id）
	 */
	private Long handleId;
}
