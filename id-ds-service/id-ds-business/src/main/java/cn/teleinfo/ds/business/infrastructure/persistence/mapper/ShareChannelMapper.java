package cn.teleinfo.ds.business.infrastructure.persistence.mapper;

import cn.teleinfo.ds.business.domain.model.entity.DataChannelDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareChannel;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.DataChannelEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareChannelEntity;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class ShareChannelMapper {

	public ShareChannel toShareChannel(ShareChannelEntity shareChannelEntity) {
		if (shareChannelEntity == null) {
			return null;
		}
		ShareChannel shareChannel = new ShareChannel();
		BeanUtils.copyProperties(shareChannelEntity, shareChannel);
		return shareChannel;
	}

	public List<ShareChannel> toShareChannelList(List<ShareChannelEntity> shareChannelEntity) {
		if (shareChannelEntity == null || shareChannelEntity.isEmpty()) {
			return null;
		}
		ArrayList<ShareChannel> shareChannels = new ArrayList<>();
		for (ShareChannelEntity channelEntity : shareChannelEntity) {
			ShareChannel shareChannel = new ShareChannel();
			BeanUtils.copyProperties(channelEntity, shareChannel);
			shareChannels.add(shareChannel);
		}
		return shareChannels;
	}

	public ShareChannelEntity toShareChannelEntity(ShareChannel shareChannel) {
		ShareChannelEntity shareChannelEntity = new ShareChannelEntity();
		BeanUtils.copyProperties(shareChannel, shareChannelEntity);
		return shareChannelEntity;

	}

	public DataChannelDomainEntity toDataChannelDomainEntity(DataChannelEntity dataChannelEntity) {
		DataChannelDomainEntity dataChannelDomainEntity = new DataChannelDomainEntity();
		BeanUtils.copyProperties(dataChannelEntity, dataChannelDomainEntity);
		return dataChannelDomainEntity;
	}
}
