package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.application.query.ShareTaskAuthQuery;
import cn.teleinfo.ds.business.domain.model.entity.AuthStatus;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskAuthDomainEntity;
import cn.teleinfo.ds.business.domain.repository.ShareTaskAuthRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareTaskAuthDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareTaskAuthEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareTaskAuthView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.ShareTaskAuthJpaRepository;
import com.pig4cloud.pig.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AllArgsConstructor
public class ShareTaskAuthRepositoryImpl implements ShareTaskAuthRepository {

	private ShareTaskAuthJpaRepository jpaRepository;

	private final ShareTaskAuthJpaRepository shareTaskAuthJpaRepository;

	@Override
	public List<ShareTaskAuthDTO> findShareTaskAuthsByApplicationId(Long applicationId) {
		return shareTaskAuthJpaRepository.findShareTaskAuthsByApplicationId(applicationId);
	}

	@Override
	public Long save(ShareTaskAuthDomainEntity shareTaskAuthDomainEntity) {
		ShareTaskAuthEntity shareTaskAuthEntity = BeanUtil.copyProperties(shareTaskAuthDomainEntity, ShareTaskAuthEntity.class);
		shareTaskAuthEntity.setCreateBy(null);
		shareTaskAuthEntity.setCreateTime(null);
		shareTaskAuthEntity.setUpdateBy(null);
		shareTaskAuthEntity.setUpdateTime(null);
		shareTaskAuthJpaRepository.save(shareTaskAuthEntity);
		return shareTaskAuthEntity.getId();
	}

	@Override
	public PageResponse<ShareTaskAuthView> listShareTaskAuths(ShareTaskAuthQuery query, Integer page, Integer size) {
		int pageIndex = (page > 0) ? page - 1 : 0;
		Pageable pageable = PageRequest.of(pageIndex, size);
		Page<ShareTaskAuthView> taskAuths = jpaRepository.findShareTaskAuths(query.getTaskNo(), query.getAuthStatus(), query.getStartTime(), query.getEndTime(), pageable);
		return new PageResponse<>(taskAuths.toList(), taskAuths.getTotalElements(), (long) size, (long) page,
				(long) taskAuths.getTotalPages());
	}

	@Override
	public ShareTaskAuthEntity findById(Long id) {
		return jpaRepository.findById(id).orElse(null);
	}

	@Override
	public ShareTaskAuthDomainEntity findShareTaskAuthByPass(Long shareTaskApplicationsId) {
		// 查询第一条审核通过的信息
		ShareTaskAuthEntity shareTaskAuthEntity = shareTaskAuthJpaRepository.findFirstByShareTaskApplicationsIdAndAuthStatus(shareTaskApplicationsId, AuthStatus.PASS.code());
		if (shareTaskAuthEntity == null) {
			return null;
		}
		return BeanUtil.copyProperties(shareTaskAuthEntity, ShareTaskAuthDomainEntity.class);
	}
}
