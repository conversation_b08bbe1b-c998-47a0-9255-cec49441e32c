package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceItems;
import cn.teleinfo.ds.business.domain.repository.TargetSourceRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.TargetSourceEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.TargetSourceJpaRepository;
import com.pig4cloud.pig.common.core.util.PageResponse;
import jakarta.persistence.criteria.Predicate;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
@AllArgsConstructor
public class TargetSourceRepositoryImpl implements TargetSourceRepository {
	private final TargetSourceJpaRepository targetSourceJpaRepository;

	@Override
	@Transactional(rollbackFor = RuntimeException.class)
	public void save(TargetSourceDomainEntity targetSourceDomainEntity) {
		TargetSourceEntity entity = BeanUtil.copyProperties(targetSourceDomainEntity, TargetSourceEntity.class);
		targetSourceJpaRepository.save(entity);
	}

	/**
	 * 查询应用下目标源名称是否存在
	 *
	 * @param appHandleCode    app handle code
	 * @param targetSourceName 目标源名称
	 * @return > 0 存在
	 */
	@Override
	public Integer findByAppHandleCodeAndTargetSourceNameCount(String appHandleCode, String targetSourceName) {
		TargetSourceEntity entity = targetSourceJpaRepository.findFirstByAppHandleCodeAndTargetSourceName(appHandleCode, targetSourceName);
		if (entity != null) {
			return 1;
		}

		return 0;
	}

	@Override
	public PageResponse<TargetSourceDomainEntity> listTargetSource(String appHandleCode,
																   String targetSourceName,
																   Integer platformType,
																   Date start,
																   Date end,
																   Integer page,
																   Integer size) {
		int pageIndex = (page > 0) ? page - 1 : 0;
		Sort sort = Sort.by(Sort.Direction.DESC, "updateTime");
		Pageable pageable = PageRequest.of(pageIndex, size, sort);

		Page<TargetSourceEntity> p = targetSourceJpaRepository.findAll((root, query, cb) -> {
			List<Predicate> predicates = new ArrayList<>();

			if (StringUtils.hasText(appHandleCode)) {
				predicates.add(cb.equal(root.get("appHandleCode"), appHandleCode));
			}

			if (StringUtils.hasText(targetSourceName)) {
				predicates.add(cb.like(root.get("targetSourceName"), "%" + targetSourceName + "%"));
			}

			if (platformType != null) {
				predicates.add(cb.equal(root.get("platformType"), platformType));
			}


			// 时间范围
			if (start != null && end != null) {
				predicates.add(cb.between(root.get("updateTime"), start, end));
			} else if (start != null) {
				predicates.add(cb.greaterThanOrEqualTo(root.get("updateTime"), end));
			}

			return cb.and(predicates.toArray(new Predicate[0]));
		}, pageable);

		List<TargetSourceDomainEntity> records = p.stream().map(e -> BeanUtil.copyProperties(e, TargetSourceDomainEntity.class)).toList();

		return new PageResponse<>(records, p.getTotalElements(), (long) size, (long) page, (long) p.getTotalPages());
	}

	@Override
	public TargetSourceDomainEntity findById(Long id) {
		Optional<TargetSourceEntity> option = targetSourceJpaRepository.findById(id);
		return option.map(entity -> {
			TargetSourceDomainEntity e = BeanUtil.copyProperties(entity, TargetSourceDomainEntity.class);
			e.setTargetSourceItems(JSONUtil.toBean(e.getItems(), TargetSourceItems.class));
			return e;
		}).orElse(null);
	}
}
