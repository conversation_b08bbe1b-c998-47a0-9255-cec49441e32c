package cn.teleinfo.ds.business.application.query;

import com.pig4cloud.pig.common.core.util.PageRequest;
import lombok.Data;

import java.util.Date;
@Data
public class ListTargetSourceQuery extends PageRequest {
	/**
	 * app handle code
	 */
	private String appHandleCode;

	/**
	 * 目标源名称
	 */
	private String targetSourceName;

	/**
	 * 平台类型 0 华为 1 阿里 2 自建
	 */
	private Integer platformType;

	/**
	 * 操作时间
	 */
	private Date start;
	private Date end;


}
