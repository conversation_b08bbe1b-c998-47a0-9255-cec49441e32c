package cn.teleinfo.ds.business.interfaces.rest;

import cn.teleinfo.ds.business.application.service.DataSyncApplicationService;
import cn.teleinfo.ds.business.interfaces.dto.response.SysConnectionResponse;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.common.security.annotation.Inner;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 内部调用 同步数据接口
 */
@RestController
@RequestMapping("")
@AllArgsConstructor
public class DataSyncController {

	private final DataSyncApplicationService dataSyncApplicationService;

//	/**
//	 * 定时任务调用 同步用户信息
//	 *
//	 * @return
//	 */
//	@GetMapping(value = "/integrated-users")
//	public R integratedUsers() {
//		dataSyncApplicationService.integratedUsers();
//		return R.ok();
//	}

	/**
	 * 定时任务调用 同步应用信息
	 *
	 * @return
	 */
	@Inner
	@GetMapping(value = "/integrated-applications")
	public R integratedApps() {
		dataSyncApplicationService.integratedApplications();
		return R.ok();
	}

	/**
	 * 定时任务调用 同步通道信息
	 *
	 * @return
	 */
	@Inner
	@GetMapping(value = "/integrated-data-channels")
	public R integratedChannels() {
		dataSyncApplicationService.integratedDataChannels();
		return R.ok();
	}

	/**
	 * 定时任务调用 同步标识信息
	 *
	 * @return
	 */
	@Inner
	@GetMapping(value = "/integrated-handles")
	public R integratedHandles() {
		dataSyncApplicationService.integratedHandles();
		return R.ok();
	}

}
