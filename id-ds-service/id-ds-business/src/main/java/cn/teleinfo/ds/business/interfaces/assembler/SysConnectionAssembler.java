package cn.teleinfo.ds.business.interfaces.assembler;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.application.command.UpdateConnectionSettingCommand;
import cn.teleinfo.ds.business.domain.model.entity.SysConnectionDomainEntity;
import cn.teleinfo.ds.business.interfaces.dto.request.UpdateConnectionSettingRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.SysConnectionResponse;
import org.springframework.stereotype.Component;

/**
 * 系统设置
 * 连接管理
 * 装配器
 */
@Component
public class SysConnectionAssembler {

	public SysConnectionResponse toResponse(SysConnectionDomainEntity conn) {
		var r = new SysConnectionResponse();
		r.setPlatformConnection(conn.getPlatformConnection());
		r.setPlatformType(conn.getPlatformType().code());
		return r;
	}

	public UpdateConnectionSettingCommand toUpdateConnectionSettingCommand(UpdateConnectionSettingRequest request) {
		UpdateConnectionSettingCommand command = new UpdateConnectionSettingCommand();

		if (!JSONUtil.isTypeJSON(request.getPlatformConnection())) {
			throw new IllegalArgumentException("连接信息错误");
		}

		command.setPlatformConnection(request.getPlatformConnection());
		command.setPlatformType(request.getPlatformType());
		return command;
	}
}
