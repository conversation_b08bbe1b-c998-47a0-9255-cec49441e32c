package cn.teleinfo.ds.business.application.service;

import cn.teleinfo.ds.business.application.command.CreateTargetSourceCommand;
import cn.teleinfo.ds.business.application.query.ListTargetSourceQuery;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceDomainEntity;
import com.pig4cloud.pig.common.core.util.PageResponse;

public interface TargetSourceApplicationService {

	/**
	 * 创建目标源
	 * @param command command
	 */
	void createTargetSource(CreateTargetSourceCommand command);

	/**
	 * 查询目标源
	 * @param query query
	 */
	PageResponse<TargetSourceDomainEntity> listTargetSource(ListTargetSourceQuery query);
}
