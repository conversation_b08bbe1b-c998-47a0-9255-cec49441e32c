package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.teleinfo.ds.business.application.query.ListShareChannelsQuery;
import cn.teleinfo.ds.business.domain.model.entity.DataChannelDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareChannel;
import cn.teleinfo.ds.business.domain.repository.ShareChannelRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsDetailsDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsVersionDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.DataChannelEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareChannelEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsApplicationsDetailView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsApplicationsView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsVersionSqlView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.DataChannelJpaRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.ShareChannelJpaRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.mapper.ShareChannelMapper;
import com.pig4cloud.pig.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@AllArgsConstructor
@Component
public class ShareChannelRepositoryImpl implements ShareChannelRepository {

	private final ShareChannelJpaRepository shareChannelJpaRepository;

	private final ShareChannelMapper shareChannelMapper;

	private final DataChannelJpaRepository dataChannelJpaRepository;

	/**
	 * 查询共享通道列表
	 */
	@Override
	public PageResponse<ShareChannelsDTO> listShareChannels(ListShareChannelsQuery query, Integer page, Integer size) {
		int pageIndex = (page > 0) ? page - 1 : 0;
		Pageable pageable = PageRequest.of(pageIndex, size);
		var results = shareChannelJpaRepository.listShareChannels(query.getChannelName(), query.getHandle(),
				query.getStatus(), query.getStartTime(), query.getEndTime(), query.getAppHandle(), query.getUserAppHandle(), pageable);

		return new PageResponse<>(results.toList(), results.getTotalElements(), (long) size, (long) page,
				(long) results.getTotalPages());
	}

	@Override
	public ShareChannelsDetailsDTO queryShareChannelsDetails(String id) {
		return shareChannelJpaRepository.queryDetailsByShareChannelIdAndStatus(id);
	}

	@Override
	public List<ShareChannelsDetailsDTO> listShareChannelsVersion(String shareChannelId) {
		return shareChannelJpaRepository.listShareChannelsVersion(shareChannelId);
	}

	@Override
	public ShareChannelsVersionSqlView queryVersionSql(String shareChannelId, String main_version,
			String minor_version) {
		return shareChannelJpaRepository.queryVersionSql(shareChannelId, main_version, minor_version);
	}

	@Override
	public List<ShareChannelsVersionDTO> queryShareChannelsVersionDTO(String shareChannelId) {
		return shareChannelJpaRepository.queryShareChannelsVersionDTO(shareChannelId);
	}

	@Override
	public PageResponse<ShareChannelsApplicationsView> listShareChannelsApplications(ListShareChannelsQuery query,
			Integer page, Integer size) {
		int pageIndex = (page > 0) ? page - 1 : 0;
		Pageable pageable = PageRequest.of(pageIndex, size);
		var results = shareChannelJpaRepository.listShareChannelsApplications(query.getChannelName(), query.getHandle(),
				query.getStatus(), query.getStartTime(), query.getEndTime(), query.getAppHandle(), pageable);

		return new PageResponse<>(results.toList(), results.getTotalElements(), (long) size, (long) page,
				(long) results.getTotalPages());
	}

	@Override
	public ShareChannelsApplicationsDetailView queryShareChannelsApplicationDetails(String applicationId) {
		return shareChannelJpaRepository.queryShareChannelsApplicationDetails(applicationId);
	}

	@Override
	public List<ShareChannel> findShareChannelsByDataChannelId(Long dataChannelId) {
		List<ShareChannel> shareChannelList = new ArrayList<>();
		List<ShareChannelEntity> channels = shareChannelJpaRepository.findByDataChannelId(dataChannelId);
		if (channels.isEmpty()) {
			return shareChannelList;
		}
		for (ShareChannelEntity channel : channels) {
			ShareChannel shareChannel = shareChannelMapper.toShareChannel(channel);
			shareChannelList.add(shareChannel);
		}
		return shareChannelList;
	}

	@Override
	public void saveShareChannel(ShareChannel shareChannel) {
		ShareChannelEntity shareChannelEntity = shareChannelMapper.toShareChannelEntity(shareChannel);
		shareChannelJpaRepository.save(shareChannelEntity);

	}

	@Override
	public ShareChannel findEnableChannel(Long shareChannelId) {
		ShareChannelEntity shareChannel = shareChannelJpaRepository.findByShareChannelIdAndEnable(shareChannelId, 1);
		return shareChannelMapper.toShareChannel(shareChannel);
	}

	@Override
	public DataChannelDomainEntity findDataChannel(Long shareChannelId) {
		Optional<DataChannelEntity> dataChannel = dataChannelJpaRepository.findById(shareChannelId);
		if (dataChannel.isPresent()) {
			return shareChannelMapper.toDataChannelDomainEntity(dataChannel.get());
		}
		return null;
	}

	@Override
	public ShareChannel findMaxVersion(Long shareChannelId) {
		ShareChannelEntity shareChannel = shareChannelJpaRepository.findMaxVersionByShareChannelId(shareChannelId);
		return shareChannelMapper.toShareChannel(shareChannel);
	}

	@Override
	public void updateEnableByShareChannelIdAndVersion(String shareChannelId, Integer mainVersion,
			Integer minorVersion) {
		shareChannelJpaRepository.updateDisEnableByShareChannelId(shareChannelId);
		shareChannelJpaRepository.updateEnableByShareChannelIdAndVersion(shareChannelId, mainVersion, minorVersion);
	}

	@Override
	public List<String> queryChannelVersions(String shareChannelId) {
		return shareChannelJpaRepository.queryChannelVersions(shareChannelId);
	}

	@Override
	public ShareChannel findByShareChannelIdAndVersion(Long shareChannelId, Integer mainVersion, Integer minorVersion) {
		ShareChannelEntity shareChannel = shareChannelJpaRepository
			.findByShareChannelIdAndMainVersionAndMinorVersion(shareChannelId, mainVersion, minorVersion);
		return shareChannelMapper.toShareChannel(shareChannel);
	}

	@Override
	public List<ShareChannel> findAllShareChannel() {
		List<ShareChannelEntity> detectChannel = shareChannelJpaRepository.findDetectChannel();
		return shareChannelMapper.toShareChannelList(detectChannel);
	}

	@Override
	public List<ShareChannel> findByShareChannelIdAndStatus(Long shareChannelId, Integer status) {
		List<ShareChannelEntity> shareChannelList = shareChannelJpaRepository
				.findAllByShareChannelIdAndStatus(shareChannelId, status);
		return shareChannelMapper.toShareChannelList(shareChannelList);
	}

}
