package cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection;

import java.time.LocalDateTime;

public interface ShareChannelsApplicationsView {

	String getId();

	String getShareChannelId();

	String getDataChannelId();

	String getApplicationId();

	String getShareChannelName();

	String getHandle();

	String getDataType();

	Integer getMainVersion();

	Integer getMinorVersion();

	String getStatus();

	LocalDateTime getUpdatedTime();


}
