package cn.teleinfo.ds.business.domain.service.impl;

import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.domain.model.aggregate.SharedTask;
import cn.teleinfo.ds.business.domain.model.aggregate.SysConnection;
import cn.teleinfo.ds.business.domain.model.entity.ShareDataSourcesDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareDataSourcesItem;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDomainEntity;
import cn.teleinfo.ds.business.domain.repository.HcsRepository;
import cn.teleinfo.ds.business.domain.repository.SharedTaskRepository;
import cn.teleinfo.ds.business.domain.service.SharedTaskDomainService;
import cn.teleinfo.ds.business.infrastructure.external.hcs.HcsClient;
import com.huaweicloud.sdk.dgc.v1.model.ScriptInfo;
import com.huaweicloud.sdk.dgc.v1.model.ShowScriptResponse;
import com.pig4cloud.pig.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@AllArgsConstructor
public class SharedTaskDomainServiceImpl implements SharedTaskDomainService {
	private final SharedTaskRepository sharedTaskRepository;
	private final HcsRepository hcsRepository;


	@Override
	public PageResponse<SharedTaskDomainEntity> listSharedTask(SharedTaskDomainEntity entity, LocalDateTime start, LocalDateTime end, Integer page, Integer size) {
		return sharedTaskRepository.listSharedTask(entity, start, end, page, size);
	}

	@Override
	public SharedTaskDomainEntity getSharedTask(Long id) {
		return sharedTaskRepository.findById(id);
	}


	/**
	 * 创建输出表结构
	 *
	 * @param sharedTask 共享任务
	 */
	@Override
	public void createOutputTables(SharedTask sharedTask) {
		SysConnection sysConnection = new SysConnection(sharedTask.getSysConnection());
		String ak = sysConnection.getHcsConnContent().getAk();
		String sk = sysConnection.getHcsConnContent().getSk();
		List<String> endpoints = List.of(sysConnection.getHcsConnContent().getDgcEndpoint());

		// 共享源
		ShareDataSourcesDomainEntity shareDataSources = sharedTask.getAppInfo().getShareDataSources();

		ShareDataSourcesItem shareDataSourcesItem = shareDataSources.getShareDataSourcesItem();

		String projectId = shareDataSourcesItem.getProjectId();
		String workspace = shareDataSourcesItem.getWorkspace();
		String stdDataConnName = shareDataSourcesItem.getStdDataConnName();
		String stdDataDatabaseName = shareDataSourcesItem.getStdDataDatabaseName();

		String scriptName = sharedTask.getSharedTask().getTaskNo();  // TODO 脚本名称暂定任务名称
		String scriptContent = sharedTask.genOutputTablesSQL();


		ScriptInfo script = hcsRepository.findScript(ak, sk, projectId, endpoints, workspace, scriptName);
		if (script != null) {
			hcsRepository.deleteScript(ak, sk, projectId, endpoints, workspace, scriptName);
		}

		// 创建脚本
		hcsRepository.createScript(ak, sk, projectId, endpoints, scriptName, scriptContent, workspace, stdDataConnName, stdDataDatabaseName);
		// 执行脚本
		String instanceId = hcsRepository.executeScript(ak, sk, projectId, endpoints, scriptName, workspace);
		// 查询脚本实例执行结果
		Map<String, String> results = hcsRepository.listScriptResults(ak, sk, projectId, endpoints, scriptName, workspace, instanceId);

		log.info("createOutputTables results={}", JSONUtil.toJsonPrettyStr(results));
	}

	/**
	 * 输出内容到指定数据源
	 *
	 * @param sharedTask
	 */
	@Override
	public void outputToTargetDataSource(SharedTask sharedTask) {

	}
}
