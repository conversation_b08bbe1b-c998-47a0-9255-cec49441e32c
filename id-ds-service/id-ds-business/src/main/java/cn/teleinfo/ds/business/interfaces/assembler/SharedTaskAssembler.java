package cn.teleinfo.ds.business.interfaces.assembler;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.application.query.ListSharedTaskQuery;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDomainEntity;
import cn.teleinfo.ds.business.interfaces.dto.request.ListSharedTaskRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.ListHandlesResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.ListSharedTaskResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.SharedTaskResponse;
import com.pig4cloud.pig.common.core.util.PageResponse;
import org.springframework.stereotype.Component;

@Component
public class SharedTaskAssembler {
	public ListSharedTaskQuery toListSharedTaskQuery(ListSharedTaskRequest request) {
		return BeanUtil.copyProperties(request, ListSharedTaskQuery.class);
	}

	public PageResponse<ListSharedTaskResponse> toListSharedTaskResponse(PageResponse<SharedTaskDomainEntity> response) {
		var records = response.getRecords().stream().map(t -> BeanUtil.copyProperties(t, ListSharedTaskResponse.class)).toList();
		return new PageResponse<>(records, response.getTotal(), response.getSize(), response.getCurrent(), response.getPages());
	}

	public SharedTaskResponse toSharedTaskResponse(SharedTaskDomainEntity sharedTask) {
		return BeanUtil.copyProperties(sharedTask, SharedTaskResponse.class);
	}
}
