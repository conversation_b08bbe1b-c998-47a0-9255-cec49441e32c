package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.domain.model.entity.ShareTaskApplicationsDetailsDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareTaskApplicationsDetailDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareTaskApplicationsDetailsEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ShareTaskApplicationsDetailsJpaRepository extends BaseRepository<ShareTaskApplicationsDetailsEntity, Long> {


	List<ShareTaskApplicationsDetailsEntity> findAllByShareTaskApplicationsId(Long shareTaskApplicationsId);

	@Query(nativeQuery = true,
			value = """
						SELECT
                             s.id,
                             t.name AS handleName,
                             s.handle AS handle,
                             s.province_prefix AS provinceName,
                             s.ent_prefix AS entName,
                             s.app_handle_code AS appName,
					         s.fields
                         FROM t_share_task_applications_details s
                         LEFT JOIN t_handle t on  s.handle = t.handle
                         WHERE s.share_task_applications_id = :taskId
                           AND s.is_deleted = 0;
					"""
	)
	List<ShareTaskApplicationsDetailDTO> findShareTaskApplicationsDetailsByTaskId(Long taskId);


	List<ShareTaskApplicationsDetailsDomainEntity> findByShareTaskApplicationsId(Long shareTaskApplicationsId);

	ShareTaskApplicationsDetailsEntity findShareTaskApplicationsDetailsById(Long id);

} 