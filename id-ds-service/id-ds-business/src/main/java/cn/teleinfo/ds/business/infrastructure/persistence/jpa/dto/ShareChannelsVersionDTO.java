package cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShareChannelsVersionDTO {

	/**
	 * id
	 */
	private Long id;

	/**
	 * 共享通道id
	 */
	private Long shareChannelId;

	/**
	 * 数据通道id
	 */
	private Long dataChannelId;

	/**
	 * 共享通道名称
	 */
	private String shareChannelName;

	/**
	 * 所属标识
	 */
	private String handle;

	/**
	 * 数据类型
	 */
	private Integer dataType;

	/**
	 * 主版本号
	 */
	private Integer mainVersion;

	/**
	 * 次版本号
	 */
	private Integer minorVersion;

	/**
	 * 应用名
	 */
	private String appName;

	/**
	 * 企业名
	 */
	private String entName;

	/**
	 * 探测状态
	 */
	private Integer status;

	/**
	 * 自动sql
	 */
	private String autoSql;

	/**
	 * 手动sql
	 */
	private String editSql;

	/**
	 * 变更原因
	 */
	private String changeReason;

	/**
	 * 启用状态
	 */
	private Integer enable;

	/**
	 * 任务状态
	 */
	private Integer channelStatus;

	/**
	 * 修改人
	 */
	private String editorUser;

	/**
	 * 修改时间
	 */
	private Timestamp updatedTime;

}
