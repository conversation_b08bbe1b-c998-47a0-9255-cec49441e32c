package cn.teleinfo.ds.business.application.service;

import cn.teleinfo.ds.business.application.query.ListSharedTaskQuery;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDomainEntity;
import com.pig4cloud.pig.common.core.util.PageResponse;

public interface SharedTaskApplicationService {


	PageResponse<SharedTaskDomainEntity> listSharedTask(ListSharedTaskQuery query);

	SharedTaskDomainEntity getSharedTask(Long id);


	/**
	 * 执行共享任务
	 * @param id 任务 id
	 */
    void execute(Long id);
}
