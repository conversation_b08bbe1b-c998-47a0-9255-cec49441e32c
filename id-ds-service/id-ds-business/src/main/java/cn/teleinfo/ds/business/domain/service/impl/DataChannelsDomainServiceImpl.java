package cn.teleinfo.ds.business.domain.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.teleinfo.ds.business.application.query.DataChannelsListQuery;
import cn.teleinfo.ds.business.domain.repository.DataChannelsRepository;
import cn.teleinfo.ds.business.domain.service.DataChannelsDomainService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.DataChannelsView;
import cn.teleinfo.ds.upms.api.feign.RoleService;
import cn.teleinfo.ds.upms.api.vo.RoleCommonVO;
import com.pig4cloud.pig.common.core.constant.UserConstants;
import com.pig4cloud.pig.common.core.util.PageResponse;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.common.security.util.SecurityUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class DataChannelsDomainServiceImpl implements DataChannelsDomainService {

	private final DataChannelsRepository repository;

	private final RoleService roleService;

	/**
	 * 查询数据通道列表
	 *
	 * @param query 查询条件
	 * @return 分页数据
	 */
	@Override
	public PageResponse<DataChannelsView> listDataChannels(DataChannelsListQuery query) {
		Long id = SecurityUtils.getUser().getId();
		R<List<RoleCommonVO>> userList = roleService.getRoleListByUserId(id);
		if (ObjectUtil.isNotNull(userList.getData())) {
			RoleCommonVO sysRole = userList.getData().get(0);

			if (!StrUtil.equals(UserConstants.USER_ADMIN_CODE, sysRole.getRoleCode())) {
				query.setAppHandleCode(sysRole.getAppHandleCode());
			}
		}
		return repository.listDataChannels(query, query.getPage(), query.getSize());
	}
}
