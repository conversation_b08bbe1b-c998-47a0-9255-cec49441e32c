package cn.teleinfo.ds.business.application.service.impl;

import cn.teleinfo.ds.business.application.command.CreateShareDataSourcesCommand;
import cn.teleinfo.ds.business.application.query.ListShareDataSourcesQuery;
import cn.teleinfo.ds.business.application.service.ShareDataSourcesApplicationService;
import cn.teleinfo.ds.business.domain.model.aggregate.SysConnection;
import cn.teleinfo.ds.business.domain.model.entity.ShareDataSourcesDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.SysConnectionDomainEntity;
import cn.teleinfo.ds.business.domain.service.ShareDataSourcesDomainService;
import cn.teleinfo.ds.business.domain.service.SysConnectionDomainService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareSourceDTO;
import com.pig4cloud.pig.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class ShareDataSourcesApplicationServiceImpl implements ShareDataSourcesApplicationService {

	private final ShareDataSourcesDomainService shareDataSourcesDomainService;
	private final SysConnectionDomainService sysConnectionDomainService;

	/**
	 * 创建共享数据源
	 */
	@Override
	public void createShareDataSources(CreateShareDataSourcesCommand command) {
		SysConnectionDomainEntity sysConnectionDomainEntity = sysConnectionDomainService.findByPlatformType(command.getPlatformType());
		shareDataSourcesDomainService.createShareDataSources(new SysConnection(sysConnectionDomainEntity), command.getPlatformType(), command.getAppHandleCode(), command.getItems());

	}

	/**
	 * 查询共享源列表
	 */
	@Override
	public PageResponse<ShareSourceDTO> listShareDataSources(ListShareDataSourcesQuery query) {
		ShareDataSourcesDomainEntity entity = new ShareDataSourcesDomainEntity();
		entity.setAppHandleCode(query.getAppHandleCode());
		entity.setAppName(query.getAppName());
		entity.setPlatformType(query.getPlatformType());
		entity.setConnState(query.getConnState());

		return shareDataSourcesDomainService.listShareDataSources(entity, query.getStart(), query.getEnd(), query.getPage(), query.getSize());
	}

}
