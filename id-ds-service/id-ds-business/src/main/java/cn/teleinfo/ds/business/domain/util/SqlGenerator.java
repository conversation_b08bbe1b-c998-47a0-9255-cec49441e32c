package cn.teleinfo.ds.business.domain.util;

import com.huaweicloud.sdk.dataartsstudio.v1.model.ColumnsList;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

public class SqlGenerator {

	/**
	 * 生成建表SQL语句
	 *
	 * @param tableName 表名
	 * @param columns   字段列表
	 * @return 建表SQL
	 */
	public static String generateCreateTableSql(String tableName, List<ColumnsList> columns) {

		String format = """
				 ROW FORMAT SERDE 'org.apache.hadoop.hive.ql.io.orc.OrcSerde'
				 STORED AS INPUTFORMAT 'org.apache.hadoop.hive.ql.io.orc.OrcInputFormat'
				       OUTPUTFORMAT 'org.apache.hadoop.hive.ql.io.orc.OrcOutputFormat'
				""";
		// 按 seqNumber 排序
		columns.sort(Comparator.comparingInt(c -> c.getSeqNumber() == null ? 0 : c.getSeqNumber()));

		List<String> pkList = new ArrayList<>();
		List<String> columnDefs = new ArrayList<>();

		for (ColumnsList col : columns) {
			StringBuilder sb = new StringBuilder();
			sb.append("`").append(col.getColumnName()).append("` ")
					.append(col.getColumnType());

			if (col.getComment() != null && !col.getComment().isEmpty()) {
				sb.append(" COMMENT '").append(col.getComment().replace("'", "''")).append("'");
			}

			columnDefs.add(sb.toString());

			if (Boolean.TRUE.equals(col.getPrimary())) {
				pkList.add("`" + col.getColumnName() + "`");
			}
		}

		StringBuilder sql = new StringBuilder();
		sql.append("CREATE TABLE `").append(tableName).append("` (\n  ");
		sql.append(String.join(",\n  ", columnDefs));
		if (!pkList.isEmpty()) {
			sql.append(",\n  PRIMARY KEY (").append(String.join(", ", pkList)).append(")");
		}
		sql.append("\n)").append(format);

		return sql.toString();
	}

	/**
	 * @param databaseName 数据库名
	 * @param fromTable    来源表
	 * @param toTable      目标表
	 * @param columnName   类字段
	 * @return
	 */
	public static String generateInsertSql(String databaseName, String fromTable, String toTable, String columnName) {
		// columnName 按 & 分割
		String[] columns = columnName.split("&");
		String selectColumns = String.join(", ", columns);
		String sql = String.format(
				"INSERT OVERWRITE TABLE %s.%s\nSELECT %s\nFROM %s.%s",
				databaseName, toTable, selectColumns, databaseName, fromTable
		);
		return sql;
	}

} 