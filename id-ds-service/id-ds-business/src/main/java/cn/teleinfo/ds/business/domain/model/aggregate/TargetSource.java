package cn.teleinfo.ds.business.domain.model.aggregate;

import cn.teleinfo.ds.business.domain.model.entity.TargetSourceDomainEntity;
import cn.teleinfo.ds.business.domain.model.valueobject.AppHandleCode;
import lombok.Data;

/**
 * 目标源聚合根
 */
@Data
public class TargetSource {
	/**
	 * 目标源
	 */
	private TargetSourceDomainEntity targetSourceDomainEntity;

	/**
	 * app handle code
	 */
	private AppHandleCode appHandleCode;


	public TargetSource(TargetSourceDomainEntity targetSourceDomainEntity, AppHandleCode appHandleCode) {
		this.targetSourceDomainEntity = targetSourceDomainEntity;
		this.appHandleCode = appHandleCode;
	}

	public TargetSource(TargetSourceDomainEntity targetSourceDomainEntity) {
		this.targetSourceDomainEntity = targetSourceDomainEntity;
	}
}
