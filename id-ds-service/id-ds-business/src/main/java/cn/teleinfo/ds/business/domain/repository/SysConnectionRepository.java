package cn.teleinfo.ds.business.domain.repository;

import cn.teleinfo.ds.business.domain.model.entity.SysConnectionDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.SysConnectionPlatformType;
import cn.teleinfo.ds.business.interfaces.dto.request.UpdateConnectionSettingRequest;

import java.util.Optional;

public interface SysConnectionRepository {

	/**
	 * 查询连接设置
	 *
	 * @param platformType 平台类型
	 * @return 连接设置信息
	 */
	SysConnectionDomainEntity findByPlatformType(SysConnectionPlatformType platformType);

	/**
	 * 更新连接设置
	 */
	void updateConnectionSetting(Integer platformType,String platformConnection);
}
