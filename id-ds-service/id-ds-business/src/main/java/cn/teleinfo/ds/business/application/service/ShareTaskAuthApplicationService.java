package cn.teleinfo.ds.business.application.service;

import cn.teleinfo.ds.business.application.command.ReviewCommand;
import cn.teleinfo.ds.business.application.query.ShareTaskAuthQuery;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskApplicationDetails;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareTaskAuthView;
import cn.teleinfo.ds.business.interfaces.dto.request.ListShareTaskAuthsRequest;
import com.pig4cloud.pig.common.core.util.PageResponse;

public interface ShareTaskAuthApplicationService {

	void review(ReviewCommand reviewCommand);

	PageResponse<ShareTaskAuthView> listShareTaskAuths(ShareTaskAuthQuery query, ListShareTaskAuthsRequest request);

	ShareTaskApplicationDetails getShareTaskAuthDetail(Long shareTaskAuthId);

}
