package cn.teleinfo.ds.business.domain.service.impl;

import cn.teleinfo.ds.business.domain.model.aggregate.SysConnection;
import cn.teleinfo.ds.business.domain.model.entity.ShareDataSourcesDomainEntity;
import cn.teleinfo.ds.business.domain.repository.ShareDataSourcesRepository;
import cn.teleinfo.ds.business.domain.service.ShareDataSourcesDomainService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareSourceDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareSourceEntity;
import com.pig4cloud.pig.common.core.exception.CheckedException;
import com.pig4cloud.pig.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@AllArgsConstructor
@Service
public class ShareDataSourcesDomainServiceImpl implements ShareDataSourcesDomainService {
	private ShareDataSourcesRepository shareDataSourcesRepository;

	@Override
	public void createShareDataSources(SysConnection sysConnection, Integer platformType, String appHandleCode, String items) {
		sysConnection.check();

		// 同一应用下只能创建一个共享源！
		ShareDataSourcesDomainEntity shareDatasource = shareDataSourcesRepository.findByAppHandleCode(appHandleCode);
		if (shareDatasource != null) {
			throw new CheckedException("同一应用下只能创建一个共享源！");
		}

		ShareSourceEntity entity = new ShareSourceEntity();
		entity.setAppHandleCode(appHandleCode);
		entity.setPlatformType(platformType);
		entity.setItems(items);
		shareDataSourcesRepository.createShareDataSources(entity);
	}

	@Override
	public PageResponse<ShareSourceDTO> listShareDataSources(ShareDataSourcesDomainEntity entity, LocalDateTime start, LocalDateTime end,
															 Integer page, Integer size) {
		return shareDataSourcesRepository.listShareDataSources(entity, start, end, page, size);
	}

	@Override
	public ShareDataSourcesDomainEntity findByAppHandleCode(String appHandleCode) {
		return shareDataSourcesRepository.findByAppHandleCode(appHandleCode);
	}
}
