package cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.AuditableEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

/**
 * 系统设置-连接配置表
 */
@Getter
@Setter
@Entity
@Table(name = "t_connection")
@SQLDelete(sql = "update t_connection set is_deleted = null where id = ?")
@SQLRestriction("is_deleted = 0")
public class ConnectionEntity extends AuditableEntity {
	/**
	 * 平台类型 0 华为云
	 */
	@Column(name = "platform_type")
	private Integer platformType;

	/**
	 * 连接信息
	 */
	@Column(name = "platform_connection")
	private String platformConnection;
}
