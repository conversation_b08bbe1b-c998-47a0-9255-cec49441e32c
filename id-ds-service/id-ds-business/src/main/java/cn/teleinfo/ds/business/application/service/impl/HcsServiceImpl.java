package cn.teleinfo.ds.business.application.service.impl;

import cn.teleinfo.ds.business.application.service.HcsService;
import cn.teleinfo.ds.business.domain.model.aggregate.SysConnection;
import cn.teleinfo.ds.business.domain.model.entity.SysConnectionDomainEntity;
import cn.teleinfo.ds.business.domain.service.HcsDomainService;
import cn.teleinfo.ds.business.domain.service.SysConnectionDomainService;
import com.huaweicloud.sdk.cdm.v1.model.Clusters;
import com.huaweicloud.sdk.cdm.v1.model.Links;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ApigCommodityOrder;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ApigDataSourceView;
import com.huaweicloud.sdk.dataartsstudio.v1.model.DatabasesList;
import com.huaweicloud.sdk.dataartsstudio.v1.model.Workspacebody;
import com.huaweicloud.sdk.iam.v3.model.AuthProjectResult;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

// 华为云对接
@AllArgsConstructor
@Service
public class HcsServiceImpl implements HcsService {
	private final HcsDomainService hcsDomainService;
	private final SysConnectionDomainService sysConnectionDomainService;

	/**
	 * 数据集成-规范层连接名称
	 */
	@Override
	public List<Links> findCdmConnections(Integer platformType, String projectId, String clusterId) {
		SysConnectionDomainEntity sysConnectionDomainEntity = sysConnectionDomainService.findByPlatformType(platformType);
		return hcsDomainService.findCdmConnections(new SysConnection(sysConnectionDomainEntity), projectId, clusterId);
	}

	/**
	 * 数据开发-规范层连接名称
	 */
	@Override
	public List<ApigDataSourceView> findDayuConnections(Integer platformType, String projectId,
														String workspace, Integer offset, Integer limit) {
		SysConnectionDomainEntity sysConnectionDomainEntity = sysConnectionDomainService.findByPlatformType(platformType);
		return hcsDomainService.findDayuConnections(new SysConnection(sysConnectionDomainEntity), projectId, workspace, offset, limit);
	}

	/**
	 * 数据开发-规范层数据库名称
	 */
	@Override
	public List<DatabasesList> findDayuConnectionsDatabases(Integer platformType, String projectId,
															String workspace, String connectionId, Integer offset, Integer limit) {
		SysConnectionDomainEntity sysConnectionDomainEntity = sysConnectionDomainService.findByPlatformType(platformType);
		return hcsDomainService.findDayuConnectionsDatabases(new SysConnection(sysConnectionDomainEntity), projectId, workspace, connectionId, offset, limit);
	}

	/**
	 * 获取资源空间列表
	 */
	@Override
	public List<AuthProjectResult> findProjects(Integer platformType) {
		SysConnectionDomainEntity sysConnectionDomainEntity = sysConnectionDomainService.findByPlatformType(platformType);
		return hcsDomainService.findProjects(new SysConnection(sysConnectionDomainEntity));
	}

	/**
	 * 获取数据治理中心实例列表
	 */
	@Override
	public List<ApigCommodityOrder> findDasInstances(Integer platformType, String projectId, Integer page, Integer size) {
		SysConnectionDomainEntity sysConnectionDomainEntity = sysConnectionDomainService.findByPlatformType(platformType);
		return hcsDomainService.findDasInstances(new SysConnection(sysConnectionDomainEntity), projectId, page, size);
	}

	/**
	 * 获取工作空间列表
	 */
	@Override
	public List<Workspacebody> findDasWorkspaces(Integer platformType, String projectId, String instanceId, Integer page, Integer size) {
		SysConnectionDomainEntity sysConnectionDomainEntity = sysConnectionDomainService.findByPlatformType(platformType);
		return hcsDomainService.findDasWorkspaces(new SysConnection(sysConnectionDomainEntity), projectId, instanceId, page, size);
	}

	/**
	 * 获取CDM集群名称列表
	 */
	@Override
	public List<Clusters> findCdmClusters(Integer platformType, String projectId) {
		SysConnectionDomainEntity sysConnectionDomainEntity = sysConnectionDomainService.findByPlatformType(platformType);
		return hcsDomainService.findCdmClusters(new SysConnection(sysConnectionDomainEntity), projectId);
	}
}
