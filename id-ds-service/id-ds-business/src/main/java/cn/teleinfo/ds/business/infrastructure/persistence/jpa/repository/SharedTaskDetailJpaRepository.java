package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.SharedTaskDetailEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SharedTaskDetailJpaRepository extends BaseRepository<SharedTaskDetailEntity, Long> {
	List<SharedTaskDetailEntity> findBySharedTaskId(Long id);
}