package cn.teleinfo.ds.business.domain.model.entity;

import jakarta.persistence.Column;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 共享任务-关联标识表
 */
@Data
public class ShareTaskApplicationsDetailsDomainEntity {
	private Long id;
	/**
	 * 省级前缀
	 */
	private String provincePrefix;

	/**
	 * 企业前缀
	 */
	private String entPrefix;

	/**
	 * 应用身份编码
	 */
	private String appHandleCode;

	/**
	 * 标识编码
	 */
	private String handle;

	/**
	 * 标识属性
	 */
	private String fields;

	/**
	 * 共享任务编号
	 */
	private String taskNo;

	/**
	 * 共享任务表id
	 */
	private Long shareTaskApplicationsId;

	/**
	 * 创建人
	 */
	private Long createBy;

	/**
	 * 更新人
	 */
	private Long updateBy;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;
}
