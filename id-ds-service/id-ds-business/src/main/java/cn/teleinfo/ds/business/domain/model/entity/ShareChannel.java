package cn.teleinfo.ds.business.domain.model.entity;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.AuditableEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import java.time.LocalDateTime;

/**
 * 共享通道信息表
 */
@Data
public class ShareChannel {


	/**
	 * 创建人
	 */

	private Long createBy;

	/**
	 * 更新人
	 */

	private Long updateBy;
	/**
	 * 共享通道ID
	 */

	private Long shareChannelId;

	/**
	 * 共享通道名称
	 */
	private String shareChannelName;

	/**
	 * 所属对象标识编码
	 */
	private String objectHandle;

	/**
	 * 数据通道ID
	 */
	private Long dataChannelId;

	/**
	 * 实例数据类型
	 */
	private Integer dataType;

	/**
	 * 主版本号
	 */
	private Integer mainVersion;

	/**
	 * 次版本号
	 */
	private Integer minorVersion;

	/**
	 * 自动sql
	 */
	private String autoSql;

	/**
	 * 手动sql
	 */
	private String editSql;

	/**
	 * 探测状态
	 */
	private Integer status;

	/**
	 * 启用状态
	 */
	private Integer enable;

	/**
	 * 省级前缀
	 */
	private String provincePrefix;

	/**
	 * 企业前缀
	 */
	private String entPrefix;

	/**
	 * 应用身份编码
	 */
	private String appHandleCode;

	/**
	 * 变更原因
	 */
	private String changeReason;

	private Long id;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 逻辑删除: 0 未删除 null 已删除
	 */
	private Integer isDeleted;

}