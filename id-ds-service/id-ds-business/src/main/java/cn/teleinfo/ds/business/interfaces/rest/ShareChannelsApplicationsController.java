package cn.teleinfo.ds.business.interfaces.rest;

import cn.teleinfo.ds.business.application.command.UpdateShareChannelsApplicationsCommand;
import cn.teleinfo.ds.business.application.query.ListShareChannelsQuery;
import cn.teleinfo.ds.business.application.service.ShareChannelsAppService;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareChannelsApplicationsDetail;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsApplicationsView;
import cn.teleinfo.ds.business.interfaces.assembler.ShareChannelsAssembler;
import cn.teleinfo.ds.business.interfaces.dto.request.ListShareChannelsRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.ShareChannelsApplicationsReviewRequest;
import com.pig4cloud.pig.common.core.util.PageResponse;
import com.pig4cloud.pig.common.core.util.R;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * 共享通道审核
 */
@RestController
@RequestMapping("/share-channels-applications")
@AllArgsConstructor
public class ShareChannelsApplicationsController {

	private final ShareChannelsAppService shareChannelsAppService;

	private final ShareChannelsAssembler shareChannelsAssembler;

	/**
	 * 共享通道审核列表查询
	 * @param request 过滤条件
	 */
	@GetMapping
	public R<PageResponse<ShareChannelsApplicationsView>> listShareChannelsApplications(
			@Valid ListShareChannelsRequest request) {
		if (request.getPage() == null || request.getSize() == null) {
			return R.failed("分页参数必填!");
		}
		ListShareChannelsQuery listShareChannelsQuery = shareChannelsAssembler.toListShareChannelsQuery(request);
		return R.ok(shareChannelsAppService.listShareChannelsApplications(listShareChannelsQuery));
	}

	/**
	 * 共享通道审核详情
	 * @param applicationId 申请单Id
	 */
	@GetMapping("/{applicationId}")
	public R<ShareChannelsApplicationsDetail> queryShareChannelsApplicationDetails(
			@PathVariable("applicationId") String applicationId) {
		if (StringUtils.isBlank(applicationId)) {
			return R.failed("共享通道申请单id必填!");
		}

		return R.ok(shareChannelsAppService.queryShareChannelsApplicationDetails(applicationId));
	}

	/**
	 * 共享通道审核
	 * @param applicationId 申请单Id
	 */
	@PostMapping("{applicationId}/review")
	public R reviewShareChannels(@PathVariable("applicationId") String applicationId,
					 @RequestBody @Valid ShareChannelsApplicationsReviewRequest request) {
		UpdateShareChannelsApplicationsCommand command = shareChannelsAssembler.toUpdateShareChannelsApplicationsCommand(request);
		shareChannelsAppService.reviewShareChannels(applicationId, command);
		return R.ok();
	}

}
