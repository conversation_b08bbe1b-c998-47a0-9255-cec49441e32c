package cn.teleinfo.ds.business.domain.model.aggregate;

import cn.teleinfo.ds.business.domain.model.entity.AuthStatus;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskAuthDetailsDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskAuthDomainEntity;
import cn.teleinfo.ds.business.domain.model.valueobject.AuthInfo;
import cn.teleinfo.ds.business.domain.model.valueobject.ShareTaskApplicationsId;
import com.pig4cloud.pig.common.core.exception.CheckedException;
import lombok.Data;

import java.util.List;

// 共享任务授权聚合根
@Data
public class ShareTaskAuth {
	// 共享任务申请id
	private ShareTaskApplicationsId applicationsId;

	/**
	 * 授权实体
	 */
	private ShareTaskAuthDomainEntity shareTaskAuthDomainEntity;

	/**
	 * 授权状态( 1:申请中 2:已驳回 3:已授权)
	 */
	private AuthStatus authStatus;

	/**
	 * 共享任务授权详情
	 * 每次审核，将当前共享任务信息备份下来。
	 */
	private List<ShareTaskAuthDetailsDomainEntity> details;


	/**
	 * 初始化审核聚合根
	 *
	 * @param shareTaskAuthDomainEntity 通过的审核记录
	 * @param applicationsId            共享任务申请id 值对象
	 */
	public ShareTaskAuth(ShareTaskAuthDomainEntity shareTaskAuthDomainEntity,
						 ShareTaskApplicationsId applicationsId) {
		this.applicationsId = applicationsId;

		if (shareTaskAuthDomainEntity != null) {
			this.shareTaskAuthDomainEntity = shareTaskAuthDomainEntity;
			this.authStatus = AuthStatus.findByCode(shareTaskAuthDomainEntity.getAuthStatus());
		} else {
			this.shareTaskAuthDomainEntity = new ShareTaskAuthDomainEntity();
		}

	}


	/**
	 * 授权
	 *
	 * @param authInfo 审核信息
	 */
	public void auth(AuthInfo authInfo) {
		// t_share_task_auth 表。
		// 每次审核都会增加一条记录。
		// 审核通过之后，就不能再次审核。
		// 已经存在审核通过的记录则不进行审核。

		if (this.authStatus != null) {
			if (this.authStatus == AuthStatus.PASS && authInfo.authStatus() == AuthStatus.PASS) {
				throw new CheckedException("已经授权，不能再次授权");
			} else {
				if (this.authStatus == AuthStatus.PASS) {
					throw new CheckedException("已经授权，不能驳回");
				}

				if (this.authStatus == AuthStatus.REJECT) {
					throw new CheckedException("已经驳回，不能再次驳回");
				}
			}
		}

		this.shareTaskAuthDomainEntity.setAuditUserId(authInfo.auditUserId());
		this.shareTaskAuthDomainEntity.setAuditRemark(authInfo.auditRemark());
		this.shareTaskAuthDomainEntity.setAuthStatus(authInfo.authStatus().code());
	}
}
