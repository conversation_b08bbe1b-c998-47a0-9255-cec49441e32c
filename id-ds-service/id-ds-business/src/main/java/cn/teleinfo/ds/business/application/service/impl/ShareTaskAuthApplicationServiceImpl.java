package cn.teleinfo.ds.business.application.service.impl;

import cn.teleinfo.ds.business.application.command.ReviewCommand;
import cn.teleinfo.ds.business.application.query.ShareTaskAuthQuery;
import cn.teleinfo.ds.business.application.service.ShareTaskAuthApplicationService;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskApplicationDetails;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskAuth;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskAuthDomainEntity;
import cn.teleinfo.ds.business.domain.model.valueobject.AuthInfo;
import cn.teleinfo.ds.business.domain.model.valueobject.ShareTaskApplicationsId;
import cn.teleinfo.ds.business.domain.service.ShareTaskAuthDomainService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareTaskAuthView;
import cn.teleinfo.ds.business.interfaces.dto.request.ListShareTaskAuthsRequest;
import com.pig4cloud.pig.common.core.util.PageResponse;
import com.pig4cloud.pig.common.security.util.SecurityUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@AllArgsConstructor
@Service
public class ShareTaskAuthApplicationServiceImpl implements ShareTaskAuthApplicationService {
	private final ShareTaskAuthDomainService shareTaskAuthDomainService;

	@Override
	public void review(ReviewCommand reviewCommand) {
		// 共享记录申请id
		ShareTaskApplicationsId shareTaskApplicationsId = new ShareTaskApplicationsId(reviewCommand.getShareTaskApplicationsId());

		// 授权通过记录
		ShareTaskAuthDomainEntity passEntity = shareTaskAuthDomainService.findShareTaskAuthByPass(shareTaskApplicationsId); // 查询已经授权的共享任务

		// 授权聚合根
		ShareTaskAuth shareTaskAuth = new ShareTaskAuth(passEntity, shareTaskApplicationsId);

		// 审核人
		Long id = SecurityUtils.getUser().getId();

		// 当前授权信息
		AuthInfo authInfo = new AuthInfo(id, reviewCommand.getAuditRemark(), reviewCommand.getAuthStatus());

		shareTaskAuthDomainService.review(shareTaskAuth, authInfo);
	}

	/**
	 * 查询授权列表
	 * @param query 查询参数
	 * @param request 分页参数
	 * @return 分页结果
	 */
	@Override
	public PageResponse<ShareTaskAuthView> listShareTaskAuths(ShareTaskAuthQuery query, ListShareTaskAuthsRequest request) {
		return shareTaskAuthDomainService.listShareTaskAuths(query, request);
	}

	/**
	 * 查询共享任务授权详情
	 * @param shareTaskAuthId 共享任务授权id
	 * @return 共享任务授权详情
	 */
	@Override
	public ShareTaskApplicationDetails getShareTaskAuthDetail(Long shareTaskAuthId) {
		return shareTaskAuthDomainService.getShareTaskAuthDetail(shareTaskAuthId);
	}
}
