package cn.teleinfo.ds.business.domain.repository;

import cn.teleinfo.ds.business.application.command.sharetaskapplications.ShareTaskApplicationsCommand;
import cn.teleinfo.ds.business.application.query.ShareTaskApplicationsQuery;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskApplicationsDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskApplicationDomain;
import cn.teleinfo.ds.business.domain.model.valueobject.ShareTaskApplicationsDetailsValueObject;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareTaskApplicationsView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareTaskApplicationsXqDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareTaskApplicationsDetailsEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareTaskApplicationsEntity;
import com.pig4cloud.pig.common.core.util.PageResponse;

import java.util.List;

public interface ShareTaskApplicationsRepository {

	ShareTaskApplicationsEntity createShareTaskApplicationsEntity(ShareTaskApplicationDomain entity);

	void createShareTaskApplicationsDetailsEntityS(List<ShareTaskApplicationsDetailsEntity> entityList);

	void createShareTaskApplicationsDetails(List<ShareTaskApplicationsDetailsValueObject> entity);

	/**
	 * 更新任务申请
	 *
	 * @param request 任务申请实体
	 */
	void deleteShareTaskApplicationsDetailsEntityS(ShareTaskApplicationsCommand request);

	PageResponse<ShareTaskApplicationsView> listShareTaskApplications(ShareTaskApplicationsQuery request, Integer page, Integer size);

	/**
	 * 根据id查询任务申请
	 * @param id 任务申请id
	 * @return 任务申请实体
	 */
	ShareTaskApplicationsXqDTO findShareTaskApplicationsById(Long id);


	ShareTaskApplicationsDomainEntity findShareTaskApplicationsDomainEntity(Long id);

	void updateStatusById(Long id, int code);


	ShareTaskApplicationsDomainEntity findById(Long id);


}
