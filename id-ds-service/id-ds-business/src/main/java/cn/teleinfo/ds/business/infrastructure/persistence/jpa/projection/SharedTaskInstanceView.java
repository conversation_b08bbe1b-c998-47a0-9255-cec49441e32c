package cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection;

import java.time.LocalDateTime;

public interface SharedTaskInstanceView {
    Long getId();
    Long getSharedTaskId();
    String getTaskInstanceNo();
    String getTaskName();
    String getTaskNo();
    Integer getTaskType();
    Integer getTaskStatus();
    Integer getExecutionType();
    Integer getRunStatus();
    Long getTargetSourceId();
    String getDatabaseName();
    String getAppHandleCode();
    String getEntPrefix();
    String getCronExpression();
    LocalDateTime getRunTime();
    Integer getRunDuration();
    Integer getSharedDataCount();
    String getLogPath();
    String getOperator();
}