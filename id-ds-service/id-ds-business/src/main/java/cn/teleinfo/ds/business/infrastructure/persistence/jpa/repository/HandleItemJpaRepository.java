package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.HandleItemEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface HandleItemJpaRepository extends BaseRepository<HandleItemEntity, Long> {
    List<Long> findIdBy();


	List<HandleItemEntity> findByHandleId(Long handleId);
}