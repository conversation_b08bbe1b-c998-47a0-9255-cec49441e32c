package cn.teleinfo.ds.business.domain.model.entity;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class SharedTaskInstanceDomainEntity {

	private Long id;

	/**
	 * 关联共享任务ID
	 */
	private Long sharedTaskId;

	/**
	 * 任务执行编号，如RW202506001
	 */
	private String taskInstanceNo;

	/**
	 * 任务名称
	 */
	private String taskName;

	/**
	 * 任务编号
	 */
	private String taskNo;

	/**
	 * 任务类型：1-手动任务，2-定时任务
	 */
	private Integer taskType;

	/**
	 * 任务状态：1-启用，0-禁用
	 */
	private Integer taskStatus;

	/**
	 * 执行类型：1-正式执行，2-测试执行
	 */
	private Integer executionType;

	/**
	 * 运行状态：1-运行中，2-成功，3-失败，0-未运行
	 */
	private Integer runStatus;

	/**
	 * 目标源ID
	 */
	private Long targetSourceId;

	/**
	 * 数据库名
	 */
	private String databaseName;

	/**
	 * 应用身份编码
	 */
	private String appHandleCode;

	/**
	 * 企业前缀
	 */
	private String entPrefix;

	/**
	 * CRON表达式，如0 0 12 * * ?（仅定时任务使用）
	 */
	private String cronExpression;

	/**
	 * 运行时间
	 */
	private LocalDateTime runTime;

	/**
	 * 运行时长(秒)
	 */
	private Integer runDuration;

	/**
	 * 共享数据总量
	 */
	private Integer sharedDataCount;

	/**
	 * 任务日志路径
	 */
	private String logPath;

	/**
	 * 操作人
	 */
	private String operator;
}
