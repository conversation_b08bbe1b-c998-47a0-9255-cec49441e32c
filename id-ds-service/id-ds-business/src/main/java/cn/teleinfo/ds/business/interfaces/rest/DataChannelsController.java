package cn.teleinfo.ds.business.interfaces.rest;

import cn.teleinfo.ds.business.application.query.DataChannelsListQuery;
import cn.teleinfo.ds.business.application.service.DataChannelsService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.DataChannelsView;
import cn.teleinfo.ds.business.interfaces.assembler.DataChannelsAssemble;
import cn.teleinfo.ds.business.interfaces.dto.response.ListDataChannelsResponse;
import com.pig4cloud.pig.common.core.util.PageResponse;
import com.pig4cloud.pig.common.core.util.R;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据通道
 */
@RestController
@RequestMapping("/data-channels")
@AllArgsConstructor
public class DataChannelsController {

	private final DataChannelsAssemble assemble;

	private final DataChannelsService service;

	/**
	 * 数据通道列表
	 *
	 * @param request 查询参数
	 * @return 列表
	 */
	@GetMapping
	public R<PageResponse<DataChannelsView>> listDataChannels(@Valid ListDataChannelsResponse request) {
		DataChannelsListQuery query = assemble.toQuery(request);
		return R.ok(service.listDataChannels(query));
	}
}
