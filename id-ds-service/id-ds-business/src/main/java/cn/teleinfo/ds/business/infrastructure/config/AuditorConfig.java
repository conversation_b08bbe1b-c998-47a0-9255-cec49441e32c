package cn.teleinfo.ds.business.infrastructure.config;

import com.pig4cloud.pig.common.security.service.PigUser;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Optional;

@Configuration
public class AuditorConfig implements AuditorAware<Long> {

    /**
     * 返回操作员标志信息
     *
     * @return
     */
	@Override
    public Optional<Long> getCurrentAuditor() {
		Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
		// 匿名接口直接返回
		if (authentication instanceof AnonymousAuthenticationToken) {
			return Optional.empty();
		}
		if (Optional.ofNullable(authentication).isPresent()) {
			Long userId = ((PigUser) authentication.getPrincipal()).getId();
			return Optional.of(userId);
		}
        // 用户定时任务，或者无Token调用的情况
        return Optional.ofNullable(null);
    }

}