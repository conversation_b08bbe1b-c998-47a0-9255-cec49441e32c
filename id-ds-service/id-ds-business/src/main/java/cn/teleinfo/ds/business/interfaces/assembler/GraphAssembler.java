package cn.teleinfo.ds.business.interfaces.assembler;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.domain.model.aggregate.Handle;
import cn.teleinfo.ds.business.domain.model.entity.HandleItemDomainEntity;
import cn.teleinfo.ds.business.domain.model.valueobject.Graph;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.GraphHandlesDTO;
import cn.teleinfo.ds.business.interfaces.dto.response.*;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class GraphAssembler {

	public static final int FIELD_SOURCE_EXTEND = 1;

	public static HandleItemsResponse toHandleItemResponse(Handle handleAggregate) {
		if (handleAggregate == null) {
			return null;
		}
		HandleItemsResponse response = new HandleItemsResponse();
		List<HandleItemDTO> items = new ArrayList<>();
		List<HandleItemDTO> extendItems = new ArrayList<>();
		List<HandleItemDomainEntity> handleItems = handleAggregate.getHandleItemModelEntities();
		for (HandleItemDomainEntity item : handleItems) {
			HandleItemDTO handleItemDTO = new HandleItemDTO();
			handleItemDTO.setField(item.getField());
			handleItemDTO.setDescription(item.getDescription());
			if (FIELD_SOURCE_EXTEND == item.getFieldSourceType()) {
				extendItems.add(handleItemDTO);
			} else {
				items.add(handleItemDTO);
			}
		}
		response.setItems(items);
		response.setExtendItems(extendItems);
		response.setAppName(handleAggregate.getHandleDomainEntity().getAppName());
		response.setEntName(handleAggregate.getHandleDomainEntity().getEntName());
		return response;
	}

	public HandleGraphResponse toHandleGraphResponse(Graph graph) {
		if (graph == null) {
			return null;
		}
		return HandleGraphResponse.builder()
				.edges(graph.getEdges())
				.nodes(graph.getNodes())
				.rootHandle(graph.getRootHandle())
				.build();
	}

	public HandleChildrenResponse toHandleChildrenResponse(Graph graph) {
		if (graph == null) {
			return null;
		}
		return HandleChildrenResponse.builder()
				.edges(graph.getEdges())
				.nodes(graph.getNodes())
				.build();

	}

	public List<GraphHandlesResponse> toGraphHandlesResponse(List<GraphHandlesDTO> request) {
		return BeanUtil.copyToList(request, GraphHandlesResponse.class);
	}

}
