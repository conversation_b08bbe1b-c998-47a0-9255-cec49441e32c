package cn.teleinfo.ds.business.domain.service;

import cn.teleinfo.ds.business.domain.model.aggregate.SharedTask;
import cn.teleinfo.ds.business.domain.model.aggregate.TargetSource;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDomainEntity;
import com.pig4cloud.pig.common.core.util.PageResponse;

import java.time.LocalDateTime;

public interface SharedTaskDomainService {
	PageResponse<SharedTaskDomainEntity> listSharedTask(SharedTaskDomainEntity entity,
														LocalDateTime start, LocalDateTime end, Integer page, Integer size);

	SharedTaskDomainEntity getSharedTask(Long id);

	/**
	 * 创建输出表结构
	 */
	void createOutputTables(SharedTask sharedTask);

	/**
	 * 输出内容到指定数据源
	 */
	void outputToTargetDataSource(SharedTask sharedTask);
}
