package cn.teleinfo.ds.business.domain.model.aggregate;

import cn.teleinfo.ds.business.interfaces.dto.response.ShareTaskApplicationDetailDataResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.SharedTaskInstanceResponse;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class SharedTaskInstance {
	private SharedTaskInstanceResponse basicInfo;

	private List<ShareTaskApplicationDetailDataResponse> shareData;
}
