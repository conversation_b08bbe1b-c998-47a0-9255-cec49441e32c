package cn.teleinfo.ds.business.domain.model.aggregate;

import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.domain.model.entity.SysConnectionDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.SysHcsConnectionDomainEntity;
import com.pig4cloud.pig.common.core.exception.CheckedException;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class SysConnection {
	private SysConnectionDomainEntity sysConnectionDomainEntity;

	private SysHcsConnectionDomainEntity hcsConnContent;

	public void check() {
		if (sysConnectionDomainEntity == null) {
			throw new CheckedException("未配置系统连接");
		}

		if (hcsConnContent == null) {
			throw new CheckedException("未配置系统连接");
		}
	}

	public SysConnection(SysConnectionDomainEntity sysConnectionDomainEntity) {
		this.sysConnectionDomainEntity = sysConnectionDomainEntity;

		this.hcsConnContent = switch (sysConnectionDomainEntity.getPlatformType()) {
			case HCS ->
					JSONUtil.toBean(sysConnectionDomainEntity.getPlatformConnection(), SysHcsConnectionDomainEntity.class);
			case ALI, CUSTOM -> throw new CheckedException("暂不支持其他平台");
		};
	}


}

