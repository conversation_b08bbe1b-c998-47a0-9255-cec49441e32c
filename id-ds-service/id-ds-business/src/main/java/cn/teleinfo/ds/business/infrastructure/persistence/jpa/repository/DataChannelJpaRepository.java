package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.DataChannelEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.DataChannelsView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface DataChannelJpaRepository extends BaseRepository<DataChannelEntity, Long> {

	@Query(nativeQuery = true, value = """
			SELECT
			    d.id AS id,
			    d.source_id as sourceId,
			    d.data_channel_name AS dataChannelName,
			    d.data_type AS dataType,
			    d.update_time AS updateTime,
			    d.resolve_sql AS resolveSql,
			    d.query_sql AS querySql,
			    d.object_handle AS objectHandle,
			    d.data_channel_id as dataChannelId
			    from t_data_channel d
			    where IF(:handle IS NOT NULL, d.object_handle = :handle, 1=1 )
			    AND IF(:appHandleCode IS NOT NULL, d.app_handle_code = :appHandleCode, 1=1 )
			    AND is_deleted = 0
			    
			    """, countQuery = """
			SELECT
				count(*)
				from t_data_channel d
				where IF(:handle IS NOT NULL, d.object_handle = :handle, 1=1 ) 
				AND IF(:appHandleCode IS NOT NULL, d.app_handle_code = :appHandleCode, 1=1 )
				and is_deleted = 0
				"""
	)
	Page<DataChannelsView> findListDataChannels(@Param("handle")String handle,@Param("appHandleCode")String appHandleCode, Pageable pageable);
} 