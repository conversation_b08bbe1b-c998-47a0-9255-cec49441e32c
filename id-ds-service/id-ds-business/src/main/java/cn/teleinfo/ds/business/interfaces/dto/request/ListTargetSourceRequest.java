package cn.teleinfo.ds.business.interfaces.dto.request;

import com.pig4cloud.pig.common.core.util.PageRequest;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class ListTargetSourceRequest extends PageRequest {
	/**
	 * app handle code
	 */
	@NotNull(message = "应用标识不能为空")
	private String appHandleCode;

	/**
	 * 目标源名称
	 */
	private String targetSourceName;

	/**
	 * 平台类型 0 华为 1 阿里 2 自建
	 */
	private Integer platformType;

	/**
	 * 操作时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date start;
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date end;
}
