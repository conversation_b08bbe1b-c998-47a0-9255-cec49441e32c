package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.GraphHandlesDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.HandleEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface HandleJpaRepository extends BaseRepository<HandleEntity, Long> {

	@Query(nativeQuery = true, value = "select max(update_time) from t_handle where is_deleted = 0")
	LocalDateTime findMaxUpdatedTime();

	List<Long> findIdBy();

	HandleEntity findByHandle(String handle);

	@Query(nativeQuery = true, value = "select " +
			"d.org_name as provinceName, " +
			"c.org_name as entName, " +
			"b.app_name as appName, " +
			"a.name as handleName, " +
			"a.handle as handleCode " +
			"from  t_handle a " +
			"left join t_app_info b on a.app_handle_code = b.handle_code " +
			"left join t_ent_prefix c on a.ent_prefix = c.ent_prefix " +
			"left join t_province_prefix d on a.province_prefix = d.province_prefix " +
			"where IF(:handle != '' and :handle is not null, " +
			"a.name like CONCAT('%',:handle,'%') or a.handle like CONCAT('%',:handle,'%'), 1=1) " +
			"and a.is_deleted = 0 ")
	List<GraphHandlesDTO> getGraphHandlesByHandle(@Param("handle") String handle);
}
