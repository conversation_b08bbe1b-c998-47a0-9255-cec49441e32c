package cn.teleinfo.ds.business.interfaces.dto.request;

import com.pig4cloud.pig.common.core.util.PageRequest;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class FindDasInstancesRequest extends PageRequest {
	/**
	 * 平台类型
	 * t_connection platform_type
	 */
	@NotNull(message = "平台类型 不能为空")
	private Integer platformType;
	/**
	 * 资源空间
	 */
	@NotNull(message = "资源空间 不能为空")
	private String projectId;
}
