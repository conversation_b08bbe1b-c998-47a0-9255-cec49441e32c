package cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

/**
 * 数据通道表
 */
@Getter
@Setter
@Entity
@Table(name = "t_data_channel")
@SQLDelete(sql = "update t_data_channel set is_deleted = null where id = ?")
@SQLRestriction("is_deleted = 0")
public class DataChannelEntity extends BaseEntity {

    /**
     * 通道原始id
     */
    @Column(name = "source_id")
    private Long sourceId;

    /**
     * 数据通道名称
     */
    @Column(name = "data_channel_name")
    private String dataChannelName;

    /**
     * 所属对象标识编码
     */
    @Column(name = "object_handle")
    private String objectHandle;

    /**
     * 所属对象标识类型
     */
    @Column(name = "object_handle_type")
    private Integer objectHandleType;

    /**
     * 数据通道ID
     */
    @Column(name = "data_channel_id")
    private String dataChannelId;

    /**
     * 实例数据类型
     */
    @Column(name = "data_type")
    private Integer dataType;

    /**
     * 解析sql
     */
    @Column(name = "resolve_sql", columnDefinition = "text")
    private String resolveSql;

    /**
     * 查询sql
     */
    @Column(name = "query_sql", columnDefinition = "text")
    private String querySql;

    /**
     * 省级前缀
     */
    @Column(name = "province_prefix")
    private String provincePrefix;

    /**
     * 企业前缀
     */
    @Column(name = "ent_prefix")
    private String entPrefix;

    /**
     * 应用身份编码
     */
    @Column(name = "app_handle_code")
    private String appHandleCode;

    /**
     * 是否共享 1-共享 2-未共享
     */
    @Column(name = "is_share")
    private Boolean isShare;
} 