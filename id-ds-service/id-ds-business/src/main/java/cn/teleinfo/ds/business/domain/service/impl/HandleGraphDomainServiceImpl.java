package cn.teleinfo.ds.business.domain.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.teleinfo.ds.business.domain.model.aggregate.Handle;
import cn.teleinfo.ds.business.domain.model.entity.HandleDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleItemDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleReferenceDomainEntity;
import cn.teleinfo.ds.business.domain.model.valueobject.Edge;
import cn.teleinfo.ds.business.domain.model.valueobject.Graph;
import cn.teleinfo.ds.business.domain.model.valueobject.Node;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.GraphHandlesDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.mapper.HandleGraphMapper;
import cn.teleinfo.ds.business.domain.repository.HandleGraphRepository;
import com.alibaba.cloud.commons.lang.StringUtils;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.*;
import cn.teleinfo.ds.business.domain.service.HandleGraphDomainService;

@Service
@AllArgsConstructor
public class HandleGraphDomainServiceImpl implements HandleGraphDomainService {
    private final HandleGraphRepository handleGraphRepository;
    private final HandleGraphMapper handleGraphMapper;

    @Override
    public Graph handleChildren(String handle) {
        return handleGraph(handle, 3);
    }

    @Override
    public Graph handleGraph(String handle) {
        var graph = handleGraph(handle, 4);
        graph.setRootHandle(handle);
        return graph;
    }

    private Graph handleGraph(String handle, int maxLayer) {
        Map<String, Integer> handleLayerMap = new HashMap<>();
        Map<String, Node> nodeMap = new HashMap<>();
        List<Edge> edges = new ArrayList<>();
        Set<String> visited = new HashSet<>();
        deepNode(handle, 1, null, handleLayerMap, nodeMap, edges, visited, maxLayer);
        for (var edge : edges) {
            var parent = nodeMap.get(edge.getSource());
            if (parent != null)
                parent.setHasChildren(1);
        }
        List<Node> filteredNodes = new ArrayList<>();
        List<Edge> filteredEdges = new ArrayList<>();
        for (var node : nodeMap.values()) {
            if (node.getLayer() < maxLayer)
                filteredNodes.add(node);
        }
        for (var edge : edges) {
            var sourceNode = nodeMap.get(edge.getSource());
            var targetNode = nodeMap.get(edge.getTarget());
            if (sourceNode != null && targetNode != null && sourceNode.getLayer() < maxLayer
                    && targetNode.getLayer() < maxLayer) {
                filteredEdges.add(edge);
            }
        }
        for (var node : filteredNodes) {
            if (node.getHasChildren() == null)
                node.setHasChildren(0);
        }
        Graph graph = new Graph();
        graph.setNodes(filteredNodes);
        graph.setEdges(filteredEdges);
        return graph;
    }

    private void deepNode(String handle, int layer, String parentHandle,
            Map<String, Integer> handleLayerMap,
            Map<String, Node> nodeMap,
            List<Edge> edges,
            Set<String> visited,
            int maxLayer) {
        if (layer > maxLayer || visited.contains(handle))
            return;
        visited.add(handle);
        HandleDomainEntity entity = handleGraphRepository.findHandleDomainByHandle(handle);
        if (entity == null)
            return;
        if (handleLayerMap.containsKey(handle) && handleLayerMap.get(handle) <= layer)
            return;
        handleLayerMap.put(handle, layer);
        Node node = new Node();
        node.setId(handle);
        node.setCode(handle);
        node.setLabel(entity.getName());
        node.setLayer(layer);
        node.setType(entity.getEntityType());
        nodeMap.put(handle, node);
        if (parentHandle != null) {
            Edge edge = new Edge();
            edge.setSource(parentHandle);
            edge.setTarget(handle);
            edges.add(edge);
        }
        List<HandleItemDomainEntity> items = handleGraphRepository.findHandleItemsByHandleId(entity.getId());
        for (HandleItemDomainEntity item : items) {
            if (item.getFieldType() == 2) {
                String childHandle = item.getFieldValue();
                if (childHandle.contains(",")) {
                    String[] handles = childHandle.split(",");
                    for (String handle1 : handles) {
                        deepNode(handle1, layer + 1, handle, handleLayerMap, nodeMap, edges, visited, maxLayer);
                    }
                } else {
                    deepNode(childHandle, layer + 1, handle, handleLayerMap, nodeMap, edges, visited, maxLayer);
                }
            } else if (item.getFieldType() == 3) {
                List<HandleReferenceDomainEntity> refs = handleGraphRepository
                        .findReferencesByHandleItemId(item.getId());
                for (HandleReferenceDomainEntity ref : refs) {
                    String childHandle = ref.getReferenceHandle();
                    deepNode(childHandle, layer + 1, handle, handleLayerMap, nodeMap, edges, visited, maxLayer);
                }
            }
        }
    }

    @Override
    public List<GraphHandlesDTO> getGraphHandles(String handle) {
        return handleGraphRepository.getGraphHandles(handle);
    }

    @Override
    public Handle handleItems(String handle) {
        HandleDomainEntity handleEntity = handleGraphRepository.findHandleDomainByHandle(handle);
        if (handleEntity == null)
            return null;
        String appName = handleGraphRepository.findAppNameByHandleCode(handleEntity.getAppHandleCode());
        String entName = handleGraphRepository.findEntNameByEntPrefix(handleEntity.getEntPrefix());
        handleEntity.setAppName(appName);
        handleEntity.setEntName(entName);
        List<HandleItemDomainEntity> handleItemEntities = handleGraphRepository
                .findHandleItemsByHandleId(handleEntity.getId());
        List<HandleItemDomainEntity> itemVOS = new ArrayList<>();
        for (HandleItemDomainEntity itemEntity : handleItemEntities) {
            HandleItemDomainEntity itemVO = new HandleItemDomainEntity();
            BeanUtils.copyProperties(itemEntity, itemVO);
            if (itemEntity.getFieldType() == 1) {
                itemVO.setFieldValue(itemEntity.getFieldValue());
            }
            if (itemEntity.getFieldType() == 2 && StrUtil.isNotEmpty(itemEntity.getFieldValue())) {
                itemVO.setFieldValue(itemEntity.getFieldValue());
            }
            List<HandleReferenceDomainEntity> referenceEntities = handleGraphRepository
                    .findReferencesByHandleItemId(itemEntity.getId());
            if (referenceEntities != null && !referenceEntities.isEmpty()) {
                List<HandleReferenceDomainEntity> referenceVOS = new ArrayList<>();
                for (HandleReferenceDomainEntity referenceEntity : referenceEntities) {
                    HandleReferenceDomainEntity referenceVO = new HandleReferenceDomainEntity();
                    referenceVO.setReferenceHandle(referenceEntity.getReferenceHandle());
                    if (StringUtils
                            .isNotEmpty(referenceEntity.getReferenceHandleProp())) {
                        referenceVO.setReferenceHandleProp(referenceEntity.getReferenceHandleProp());
                    }
                    if (StringUtils.isNotEmpty(referenceEntity.getQueryProp())) {
                        referenceVO.setQueryProp(referenceEntity.getQueryProp());
                    }
                    if (StringUtils.isNotEmpty(referenceEntity.getParamProp())) {
                        referenceVO.setParamProp(referenceEntity.getParamProp());
                    }
                    referenceVOS.add(referenceVO);
                }
                itemVO.setReferences(referenceVOS);
            }
            itemVOS.add(itemVO);
        }
        return handleGraphMapper.toHandle(itemVOS, handleEntity);
    }
}
