package cn.teleinfo.ds.business.interfaces.rest;

import cn.teleinfo.ds.business.application.service.HandleGraphApplicationService;
import cn.teleinfo.ds.business.domain.model.aggregate.Handle;
import cn.teleinfo.ds.business.domain.model.valueobject.Graph;
import cn.teleinfo.ds.business.interfaces.assembler.GraphAssembler;
import cn.teleinfo.ds.business.interfaces.dto.response.GraphHandlesResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleChildrenResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleGraphResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleItemsResponse;
import com.pig4cloud.pig.common.core.util.R;
import lombok.AllArgsConstructor;
import org.springframework.data.repository.query.Param;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 对象标识图谱
 */
@RestController
@RequestMapping("/graph")
@AllArgsConstructor
public class HandleGraphController {

	private final HandleGraphApplicationService handleGraphApplicationService;

	private final GraphAssembler graphAssembler;

	/**
	 * 对象标识模糊查询
	 *
	 * @param handle 对象标识
	 */
	@GetMapping(value = "/handles")

	public R<List<GraphHandlesResponse>> listShareDataSources(@RequestParam("handle") String handle) {
		var GraphHandlesDTOList = handleGraphApplicationService.getGraphHandles(handle);
		var graphHandlesResponse = graphAssembler.toGraphHandlesResponse(GraphHandlesDTOList);
		return R.ok(graphHandlesResponse);
	}

	/**
	 * 获取标识图谱
	 *
	 * @return
	 */
	@GetMapping(value = "")
	public R graph(@RequestParam String handle) {
		Graph handleGraph = handleGraphApplicationService.handleGraph(handle);
		HandleGraphResponse handleGraphResponse = graphAssembler.toHandleGraphResponse(handleGraph);
		return R.ok(handleGraphResponse);
	}

	/**
	 * 获取标识子节点
	 *
	 * @return
	 */
	@GetMapping(value = "/handles/children")
	public R handleChildren(@RequestParam String handle) {
		Graph handleChildren = handleGraphApplicationService.handleChildren(handle);
		HandleChildrenResponse handleChildrenResponse = graphAssembler.toHandleChildrenResponse(handleChildren);
		return R.ok(handleChildrenResponse);
	}

	/**
	 * 获取标识属性
	 *
	 * @return
	 */
	@GetMapping(value = "/handles/items")
	public R handleItem(@RequestParam String handle) {
		Handle handleAggregate = handleGraphApplicationService.handleItems(handle);
		HandleItemsResponse response = GraphAssembler.toHandleItemResponse(handleAggregate);
		return R.ok(response);
	}

}
