package cn.teleinfo.ds.business.interfaces.rest;

import cn.teleinfo.ds.business.application.command.CreateTargetSourceCommand;
import cn.teleinfo.ds.business.application.query.ListTargetSourceQuery;
import cn.teleinfo.ds.business.application.service.TargetSourceApplicationService;
import cn.teleinfo.ds.business.interfaces.assembler.TargetSourceApplicationAssembler;
import cn.teleinfo.ds.business.interfaces.dto.request.ListTargetSourceRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.CreateTargetSourceRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.ListTargetSourceResponse;
import com.pig4cloud.pig.common.core.util.PageResponse;
import com.pig4cloud.pig.common.core.util.R;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/target-sources")
@AllArgsConstructor
public class TargetSourceController {

	private final TargetSourceApplicationService targetSourceApplicationService;
	private final TargetSourceApplicationAssembler targetSourceApplicationAssembler;

	@PostMapping
	public R createTargetSource(@RequestBody @Valid CreateTargetSourceRequest request) {
		CreateTargetSourceCommand command = targetSourceApplicationAssembler.toCreateTargetSourceCommand(request);
		targetSourceApplicationService.createTargetSource(command);
		return R.ok();
	}

	@GetMapping
	public R<PageResponse<ListTargetSourceResponse>> listTargetSource(@Valid ListTargetSourceRequest request) {
		ListTargetSourceQuery query = targetSourceApplicationAssembler.toListTargetSourceQuery(request);
		return R.ok(targetSourceApplicationAssembler.toListTargetSourceResponse(targetSourceApplicationService.listTargetSource(query)));
	}
}
