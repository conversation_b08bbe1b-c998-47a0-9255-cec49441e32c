package cn.teleinfo.ds.business.application.service;

import cn.teleinfo.ds.business.application.query.SharedTaskInstanceListQuery;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskInstanceListView;
import cn.teleinfo.ds.business.interfaces.dto.response.SharedTaskInstanceDetailResponse;
import com.pig4cloud.pig.common.core.util.PageResponse;

public interface SharedTaskInstanceAppService {

	PageResponse<SharedTaskInstanceListView> listSharedTaskInstances(SharedTaskInstanceListQuery query);

	SharedTaskInstanceDetailResponse getShareTaskInstanceDetail(Long instanceId);


}
