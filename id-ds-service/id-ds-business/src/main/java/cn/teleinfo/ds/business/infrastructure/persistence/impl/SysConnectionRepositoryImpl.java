package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.domain.model.entity.SysConnectionDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.SysConnectionPlatformType;
import cn.teleinfo.ds.business.domain.model.entity.SysHcsConnectionDomainEntity;
import cn.teleinfo.ds.business.domain.repository.SysConnectionRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ConnectionEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.ConnectionJpaRepository;
import cn.teleinfo.ds.business.interfaces.dto.request.UpdateConnectionSettingRequest;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Component
@AllArgsConstructor
public class SysConnectionRepositoryImpl implements SysConnectionRepository {

	private final ConnectionJpaRepository repository;

	/**
	 * 查询连接设置
	 *
	 * @param platformType 平台类型
	 * @return 连接设置信息
	 */
	@Override
	public SysConnectionDomainEntity findByPlatformType(SysConnectionPlatformType platformType) {
		var option = repository.findByPlatformType(platformType.code());

		if (option.isEmpty()) {
			return null;
		}

		var conn = option.get();

		SysConnectionDomainEntity entity = new SysConnectionDomainEntity();
		entity.setId(conn.getId());
		entity.setPlatformType(platformType);
		entity.setPlatformConnection(conn.getPlatformConnection());

		return entity;
	}

	/**
	 * 更新连接设置
	 */
	@Override
	@Transactional(rollbackFor = RuntimeException.class)
	public void updateConnectionSetting(Integer platformType, String platformConnection) {
		var option = repository.findByPlatformType(platformType);
		var entity = option.orElse(new ConnectionEntity());
		entity.setPlatformType(platformType);
		entity.setPlatformConnection(platformConnection);
		repository.save(entity);
	}
}
