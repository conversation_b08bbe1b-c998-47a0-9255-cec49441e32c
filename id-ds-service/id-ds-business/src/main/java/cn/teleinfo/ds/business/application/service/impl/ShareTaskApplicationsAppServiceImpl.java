package cn.teleinfo.ds.business.application.service.impl;

import cn.teleinfo.ds.business.application.command.sharetaskapplications.ShareTaskApplicationsCommand;
import cn.teleinfo.ds.business.application.query.HandleItemQuery;
import cn.teleinfo.ds.business.application.query.ShareTaskApplicationsQuery;
import cn.teleinfo.ds.business.application.service.ShareTaskApplicationsAppService;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskApplicationDetails;
import cn.teleinfo.ds.business.domain.service.ShareTaskApplicationsDomainService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareTaskApplicationsView;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleApplicationResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleItemsResponse;
import com.pig4cloud.pig.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class ShareTaskApplicationsAppServiceImpl implements ShareTaskApplicationsAppService {

	private final ShareTaskApplicationsDomainService domainService;


	/**
	 * 创建共享任务申请
	 *
	 * @param command 共享任务申请命令
	 */
	@Override
	public void createShareTaskApplications(ShareTaskApplicationsCommand command) {
		domainService.createShareTaskApplications(command);
	}

	/**
	 * 更新共享任务申请
	 *
	 * @param applicationId 共享任务申请ID
	 * @param command       共享任务申请命令
	 */
	@Override
	public void updateShareTaskApplications(Long applicationId, ShareTaskApplicationsCommand command) {
		domainService.updateShareTaskApplications(applicationId, command);
	}

	/**
	 * 查询共享任务申请列表
	 *
	 * @param query   共享任务申请查询条件
	 * @return 分页查询结果
	 */
	@Override
	public PageResponse<ShareTaskApplicationsView> listShareTaskApplications(ShareTaskApplicationsQuery query) {
		return domainService.listShareTaskApplications(query);
	}

	/**
	 * 获取共享任务申请详情
	 * @param applicationId 共享任务申请ID
	 * @return 共享任务申请详情
	 */
	@Override
	public ShareTaskApplicationDetails getShareTaskApplicationDetail(Long applicationId) {
		return domainService.getShareTaskApplicationDetail(applicationId);
	}

	/**
	 * 根据handle获取handleItems
	 * @param query handle
	 * @return handleItems
	 */
	@Override
	public HandleApplicationResponse getHandleItemsByHandle(HandleItemQuery query) {
		return domainService.getHandleItemsByHandle(query);
	}
}
