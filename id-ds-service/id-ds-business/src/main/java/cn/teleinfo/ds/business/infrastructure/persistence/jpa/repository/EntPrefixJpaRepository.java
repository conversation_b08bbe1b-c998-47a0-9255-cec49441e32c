package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.EntPrefixEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EntPrefixJpaRepository extends BaseRepository<EntPrefixEntity, Long> {

	List<EntPrefixEntity> findByProvincePrefix(String provincePrefix);

	EntPrefixEntity findByEntPrefix(String entPrefix);
}