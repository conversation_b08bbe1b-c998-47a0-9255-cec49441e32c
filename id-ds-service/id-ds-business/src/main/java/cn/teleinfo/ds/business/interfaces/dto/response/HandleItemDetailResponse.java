package cn.teleinfo.ds.business.interfaces.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class HandleItemDetailResponse {

	private Long id;

	/**
	 * 属性字段
	 */
	private String field;

	/**
	 * 索引
	 */
	private Integer fieldIndex;

	/**
	 * 描述
	 */
	private String description;

	/**
	 * 属性类型（1固定值 2标识解析数据源 3标识值 4标识-属性）
	 */
	private Integer fieldType;

	/**
	 * 标识ID（关联t_handle.id）
	 */
	private Long handleId;

	/**
	 * 属性值
	 */
	private String fieldValue;

	/**
	 * 数据通道ID
	 */
	private Long dataChannelId;

	/**
	 * 属性来源类型（0基础属性 1扩展属性）
	 */
	private Integer fieldSourceType;

	/**
	 * 应用标识编码
	 */
	private String appHandleCode;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 省级前缀
	 */
	private String provincePrefix;

	/**
	 * 企业前缀
	 */
	private String entPrefix;
}
