package cn.teleinfo.ds.business.application.service.impl;

import cn.teleinfo.ds.business.application.command.CreateShareChannelVersionCommand;
import cn.teleinfo.ds.business.application.command.UpdateShareChannelsApplicationsCommand;
import cn.teleinfo.ds.business.application.query.ListShareChannelsQuery;
import cn.teleinfo.ds.business.application.service.ShareChannelsAppService;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareChannelsApplicationsDetail;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareChannelsVersion;
import cn.teleinfo.ds.business.domain.service.ShareChannelsDomainService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsDetailsDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsApplicationsView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsVersionSqlView;
import com.pig4cloud.pig.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class ShareChannelsAppServiceImpl implements ShareChannelsAppService {

	private final ShareChannelsDomainService shareChannelsDomainService;

	@Override
	public PageResponse<ShareChannelsDTO> listShareChannels(ListShareChannelsQuery query) {
		return shareChannelsDomainService.listShareChannels(query);
	}

	@Override
	public ShareChannelsDetailsDTO queryShareChannelsDetails(String id) {
		return shareChannelsDomainService.queryShareChannelsDetails(id);
	}

	@Override
	public List<ShareChannelsDetailsDTO> listShareChannelsVersion(String shareChannelId) {
		return shareChannelsDomainService.listShareChannelsVersion(shareChannelId);
	}

	@Override
	public ShareChannelsVersionSqlView queryVersionSql(String shareChannelId, String version) {
		return shareChannelsDomainService.queryVersionSql(shareChannelId, version);
	}

	@Override
	public List<ShareChannelsVersion> shareChannelsVersion(String shareChannelId) {
		return shareChannelsDomainService.shareChannelsVersion(shareChannelId);
	}

	@Override
	public PageResponse<ShareChannelsApplicationsView> listShareChannelsApplications(ListShareChannelsQuery query) {
		return shareChannelsDomainService.listShareChannelsApplications(query);
	}

	@Override
	public ShareChannelsApplicationsDetail queryShareChannelsApplicationDetails(String applicationId) {
		return shareChannelsDomainService.queryShareChannelsApplicationDetails(applicationId);
	}

	@Override
	public void createShareChannels() {
		shareChannelsDomainService.createShareChannels();
	}

	@Override
	public void createShareChannel(Long shareChannelId) {
		shareChannelsDomainService.createShareChannel(shareChannelId);

	}

	@Override
	public void reviewShareChannels(String applicationId, UpdateShareChannelsApplicationsCommand command) {
		shareChannelsDomainService.reviewShareChannels(applicationId, command);
	}

	@Override
	public List<String> queryChannelVersions(String shareChannelId) {
		return shareChannelsDomainService.queryChannelVersions(shareChannelId);
	}

	@Override
	public void shareChannelVersionSave(CreateShareChannelVersionCommand command) {
		shareChannelsDomainService.shareChannelVersionSave(command);
	}

	@Override
	public void shareChannelVersionChange(String shareChannelId, String version) {
		shareChannelsDomainService.shareChannelVersionChange(shareChannelId, version);
	}

	@Override
	public void detectShareChannels() {
		shareChannelsDomainService.detectShareChannels();

	}

}
