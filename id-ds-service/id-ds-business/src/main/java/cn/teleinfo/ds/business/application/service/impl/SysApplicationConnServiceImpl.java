package cn.teleinfo.ds.business.application.service.impl;

import cn.teleinfo.ds.business.application.command.UpdateConnectionSettingCommand;
import cn.teleinfo.ds.business.application.service.SysApplicationConnService;
import cn.teleinfo.ds.business.domain.model.entity.SysConnectionDomainEntity;
import cn.teleinfo.ds.business.domain.service.SysConnectionDomainService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;


@Service
@AllArgsConstructor
public class SysApplicationConnServiceImpl implements SysApplicationConnService {

	private final SysConnectionDomainService sysConnectionDomainService;

	/**
	 * 查询连接设置
	 *
	 * @param platformType 平台类型
	 * @return 连接设置信息
	 */
	@Override
	public SysConnectionDomainEntity getConnectionSetting(Integer platformType) {
		return sysConnectionDomainService.findByPlatformType(platformType);
	}

	/**
	 * 更新连接设置
	 *
	 * @param command 连接设置信息
	 */
	@Override
	public void updateConnectionSetting(UpdateConnectionSettingCommand command) {
		sysConnectionDomainService.updateConnectionSetting(command.getPlatformType(),command.getPlatformConnection());
	}
}
