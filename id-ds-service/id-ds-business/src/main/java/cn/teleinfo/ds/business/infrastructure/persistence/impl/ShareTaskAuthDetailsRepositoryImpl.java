package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskAuthDetailsDomainEntity;
import cn.teleinfo.ds.business.domain.service.ShareTaskAuthDetailsRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareTaskAuthDetailsEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.ShareTaskAuthDetailsJpaRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AllArgsConstructor
public class ShareTaskAuthDetailsRepositoryImpl implements ShareTaskAuthDetailsRepository {
	private final ShareTaskAuthDetailsJpaRepository shareTaskAuthDetailsJpaRepository;

	@Override
	public Long save(ShareTaskAuthDetailsDomainEntity shareTaskAuthDetailsDomainEntity) {
		ShareTaskAuthDetailsEntity entity = BeanUtil.copyProperties(shareTaskAuthDetailsDomainEntity,ShareTaskAuthDetailsEntity.class);
		entity.setCreateBy(null);
		entity.setCreateTime(null);
		entity.setUpdateBy(null);
		entity.setUpdateTime(null);
		shareTaskAuthDetailsJpaRepository.save(entity);
		return entity.getId();
	}
}
