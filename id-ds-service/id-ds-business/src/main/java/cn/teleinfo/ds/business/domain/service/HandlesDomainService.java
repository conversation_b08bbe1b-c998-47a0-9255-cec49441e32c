package cn.teleinfo.ds.business.domain.service;

import cn.teleinfo.ds.business.domain.model.aggregate.Handle;
import cn.teleinfo.ds.business.domain.model.entity.HandleDomainEntity;
import com.pig4cloud.pig.common.core.util.PageResponse;

public interface HandlesDomainService {

	/**
	 * 对象标识列表
	 */
	PageResponse<Handle> listHandles(Handle handle,Integer page,Integer size);

	/**
	 * 查询对象标识
	 */
	HandleDomainEntity findByHandle(String handle);

}
