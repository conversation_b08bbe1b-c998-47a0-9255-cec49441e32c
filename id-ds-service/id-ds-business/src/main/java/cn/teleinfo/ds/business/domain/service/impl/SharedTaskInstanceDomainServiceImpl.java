package cn.teleinfo.ds.business.domain.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.teleinfo.ds.business.application.query.SharedTaskInstanceListQuery;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskInstanceDomainEntity;
import cn.teleinfo.ds.business.domain.repository.ShareTaskApplicationsDetailRepository;
import cn.teleinfo.ds.business.domain.repository.SharedTaskInstanceRepository;
import cn.teleinfo.ds.business.domain.service.SharedTaskInstanceDomainService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareTaskApplicationsDetailDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskInstanceListView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskInstanceView;
import cn.teleinfo.ds.business.interfaces.dto.response.ShareTaskApplicationDetailDataResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.SharedTaskInstanceDetailResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.SharedTaskInstanceResponse;
import cn.teleinfo.ds.upms.api.feign.RoleService;
import cn.teleinfo.ds.upms.api.vo.RoleCommonVO;
import com.pig4cloud.pig.common.core.constant.UserConstants;
import com.pig4cloud.pig.common.core.util.PageResponse;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.common.security.util.SecurityUtils;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class SharedTaskInstanceDomainServiceImpl implements SharedTaskInstanceDomainService {

	private final SharedTaskInstanceRepository repository;

	private final ShareTaskApplicationsDetailRepository detailRepository;

	private final RoleService roleService;
	@Override
	public PageResponse<SharedTaskInstanceListView> listSharedTaskInstances(SharedTaskInstanceListQuery query) {
		Long id = SecurityUtils.getUser().getId();
		R<List<RoleCommonVO>> userList = roleService.getRoleListByUserId(id);
		if (ObjectUtil.isNotNull(userList.getData())) {
			RoleCommonVO sysRole = userList.getData().get(0);

			if (!StrUtil.equals(UserConstants.USER_ADMIN_CODE, sysRole.getRoleCode())) {
				query.setAppHandleCode(sysRole.getAppHandleCode());
			}
		}
		return repository.listSharedTaskInstances(query, query.getPage(), query.getSize());
	}

	@Override
	public SharedTaskInstanceDetailResponse getShareTaskInstanceDetail(Long instanceId) {
		SharedTaskInstanceView instanceById = repository.getSharedTaskInstanceById(instanceId);
		if (ObjectUtil.isEmpty(instanceById)) {
			return null;
		}
		SharedTaskInstanceDetailResponse instance = new SharedTaskInstanceDetailResponse();
		SharedTaskInstanceResponse instanceResponse = new SharedTaskInstanceResponse();
		BeanUtils.copyProperties(instanceById, instanceResponse);
		instance.setBasicInfo(instanceResponse);
		List<ShareTaskApplicationsDetailDTO> detailDTOS = detailRepository.findShareTaskApplicationsDetailsByTaskId(instanceById.getSharedTaskId());
		if (CollectionUtil.isEmpty(detailDTOS)) {
			return instance;
		}
		// 获取共享数据
		ArrayList<ShareTaskApplicationDetailDataResponse> detailValues = new ArrayList<>();
		detailDTOS.forEach(detail -> {
			ShareTaskApplicationDetailDataResponse value = new ShareTaskApplicationDetailDataResponse();
			BeanUtils.copyProperties(detail, value);
			detailValues.add(value);
		});
		instance.setShareData(detailValues);
		instance.setLog(readLog(instanceById.getLogPath()));
		return instance;
	}

	/**
	 * 读取日志文件内容
	 * @param logPath 日志文件路径
	 * @return 日志文件内容
	 */
	public String readLog(String logPath) {
		if (StringUtils.isBlank(logPath)) {
			throw new RuntimeException("日志路径为空!");
		}

		String normalizedPath;
		try {
			normalizedPath = new File(logPath).getCanonicalPath();
		} catch (IOException e) {
			throw new RuntimeException("无法解析日志路径: " + logPath, e);
		}

		StringBuilder sb = new StringBuilder();
		try (BufferedReader br = new BufferedReader(new FileReader(normalizedPath))) {
			String line;
			while ((line = br.readLine()) != null) {
				sb.append(line).append('\n');
			}
		} catch (IOException e) {
			throw new RuntimeException("读取日志文件失败: " + normalizedPath, e);
		}

		return sb.toString();
	}
}
