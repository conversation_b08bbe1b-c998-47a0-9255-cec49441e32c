package cn.teleinfo.ds.business.interfaces.dto.request;

import com.pig4cloud.pig.common.core.util.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ListHandlesRequest extends PageRequest {

	/**
	 * 对象标识名称
	 */
	private String name;

	/**
	 * 对象标识
	 */
	private String handle;

	/**
	 * 应用编码
	 */
	private String appHandleCode;
}
