package cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseEntityListeners;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.time.LocalDateTime;

/**
 * 共享任务明细表
 */
@Getter
@Setter
@Entity
@Table(name = "t_shared_task_detail")
@EntityListeners(BaseEntityListeners.class)
public class SharedTaskDetailEntity  {
	@Id
	private Long id;

	/**
	 * 创建时间
	 */
	@CreatedDate
	@Column(name = "create_time")
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@LastModifiedDate
	@Column(name = "update_time")
	private LocalDateTime updateTime;

    /**
     * 关联共享任务ID
     */
    @Column(name = "shared_task_id", nullable = false)
    private Long sharedTaskId;

    /**
     * 对象标识编码
     */
    @Column(name = "handle", nullable = false)
    private String handle;

    /**
     * 属性清单（JSON数组格式）
     */
    @Column(name = "fields", columnDefinition = "json")
    private String fields;

    /**
     * 应用身份编码
     */
    @Column(name = "app_handle_code")
    private String appHandleCode;

    /**
     * 企业前缀
     */
    @Column(name = "ent_prefix")
    private String entPrefix;

    /**
     * 省级前缀
     */
    @Column(name = "province_prefix")
    private String provincePrefix;
}