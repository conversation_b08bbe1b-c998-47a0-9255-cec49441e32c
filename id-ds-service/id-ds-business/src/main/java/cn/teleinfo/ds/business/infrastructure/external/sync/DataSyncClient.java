package cn.teleinfo.ds.business.infrastructure.external.sync;

import cn.teleinfo.ds.business.domain.repository.DataSyncRepository;
import cn.teleinfo.ds.business.infrastructure.config.IpProperties;
import cn.teleinfo.ds.business.infrastructure.persistence.dto.sync.SyncAppDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.dto.sync.SyncChannelDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.dto.sync.SyncHandleDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.dto.sync.SyncUserDTO;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
@AllArgsConstructor
public class DataSyncClient implements DataSyncRepository {
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;
    private final IpProperties ipProperties;



//	@Override
//    public List<SyncUserDTO> integratedUsers() {
//        List<SyncUserDTO> users = new ArrayList<>();
//        String url = ipProperties.getIp().concat(ipProperties.getSyncUserUrl());
//        Request request = new Request.Builder()
//                .url(url)
//                .get()
//                .build();
//        Response response;
//        try {
//            response = httpClient.newCall(request).execute();
//            boolean successful = response.isSuccessful();
//            if (!successful) {
//                log.error("用户同步接口连接错误, code:{}, body:{}", response.code(),
//                        response.body());
//                return users;
//            }
//            String result = response.body().string();
//            JsonNode jsonNode = objectMapper.readTree(result);
//            int code = jsonNode.get("code").asInt();
//            if (code != 200) {
//                String message = jsonNode.get("message").asText();
//                log.error("同步接口错误, code:{}, body:{}", code, message);
//                return users;
//            }
//            JsonNode data = jsonNode.get("data");
//            users = objectMapper.convertValue(data,
//                    TypeFactory.defaultInstance().constructCollectionType(List.class, SyncUserDTO.class));
//
//
//        } catch (IOException e) {
//            log.error("同步用户数据异常", e);
//        }
//        return users;
//    }

	@Override
    public List<SyncChannelDTO> integratedDataChannels() {
        List<SyncChannelDTO> channels = new ArrayList<>();
        String url = ipProperties.getIp().concat(ipProperties.getSyncChannelUrl());
        Request request = new Request.Builder()
                .url(url)
                .get()
                .build();
        Response response;
        try {
            response = httpClient.newCall(request).execute();
            boolean successful = response.isSuccessful();
            if (!successful) {
                log.error("通道同步接口连接错误, code:{}, body:{}", response.code(),
                        response.body());
                return channels;
            }
            String result = response.body().string();
            JsonNode jsonNode = objectMapper.readTree(result);
            int code = jsonNode.get("code").asInt();
            if (code != 10000) {
                String message = jsonNode.get("message").asText();
                log.error("同步接口错误, code:{}, body:{}", code, message);
                return channels;
            }
            JsonNode data = jsonNode.get("data");
            channels = objectMapper.convertValue(data,
                    TypeFactory.defaultInstance().constructCollectionType(List.class, SyncChannelDTO.class));

        } catch (IOException e) {
            log.error("同步通道数据异常", e);
        }
        return channels;
    }

	@Override
    public List<SyncAppDTO> integratedApplications() {
        List<SyncAppDTO> apps = new ArrayList<>();
        String url = ipProperties.getIp().concat(ipProperties.getSyncAppUrl());
        Request request = new Request.Builder()
                .url(url)
                .get()
                .build();
        Response response;
        try {
            response = httpClient.newCall(request).execute();
            boolean successful = response.isSuccessful();
            if (!successful) {
                log.error("应用同步接口连接错误, code:{}, body:{}", response.code(),
                        response.body());
                return apps;
            }
            String result = response.body().string();
            JsonNode jsonNode = objectMapper.readTree(result);
            int code = jsonNode.get("code").asInt();
            if (code != 10000) {
                String message = jsonNode.get("message").asText();
                log.error("同步接口错误, code:{}, body:{}", code, message);
                return apps;
            }
            JsonNode data = jsonNode.get("data");
            apps = objectMapper.convertValue(data,
                    TypeFactory.defaultInstance().constructCollectionType(List.class, SyncAppDTO.class));

        } catch (IOException e) {
            log.error("同步应用数据异常", e);
        }
        return apps;
    }

	@Override
    public List<SyncHandleDTO> integratedHandles(Integer page, Integer size, LocalDateTime updatedTime) {
        String time = updatedTime == null ? ""
                : "&updatedTime=" + updatedTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String baseUrl = ipProperties.getIp().concat(ipProperties.getSyncHandleUrl());
        List<SyncHandleDTO> handles = new ArrayList<>();
        try {
            String url = baseUrl + "?page=" + page + "&size=" + size + time;
            Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .build();
            Response response;

            response = httpClient.newCall(request).execute();
            boolean successful = response.isSuccessful();

            if (!successful) {
                log.error("同步标识接口连接错误, code:{}, body:{}", response.code(),
                        response.body());
                return handles;
            }

            String result = response.body().string();
            JsonNode jsonNode = objectMapper.readTree(result);
            int code = jsonNode.get("code").asInt();
            if (code != 10000) {
                String message = jsonNode.get("message").asText();
                log.error("同步标识接口错误, code:{}, body:{}", code, message);
                return handles;
            }

            JsonNode data = jsonNode.get("data");
            JsonNode content = data.get("content");
            handles = objectMapper.convertValue(content,
                    TypeFactory.defaultInstance().constructCollectionType(List.class, SyncHandleDTO.class));
        } catch (Exception e) {
            log.error("处理标识数据异常", e);
            throw new RuntimeException(e);
        }
        return handles;
    }
}
