package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.domain.model.entity.HandleDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleItemDomainEntity;
import cn.teleinfo.ds.business.domain.repository.HandlesRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.HandleEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.HandleItemEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.HandleItemJpaRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.HandleJpaRepository;
import com.pig4cloud.pig.common.core.util.PageResponse;
import jakarta.persistence.criteria.Predicate;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
@Component
public class HandlesRepositoryImpl implements HandlesRepository {

	private final HandleJpaRepository handleJpaRepository;
	private final HandleItemJpaRepository handleItemJpaRepository;

	@Override
	public PageResponse<HandleDomainEntity> listHandles(String appHandleCode, String name, String handle, Integer page, Integer size) {
		int pageIndex = (page > 0) ? page - 1 : 0;
		Pageable pageable = PageRequest.of(pageIndex, size);

		Page<HandleEntity> p = handleJpaRepository.findAll((root, query, cb) -> {
			List<Predicate> predicates = new ArrayList<>();

			if (StringUtils.hasText(appHandleCode)) {
				predicates.add(cb.equal(root.get("appHandleCode"), appHandleCode));
			}

			if (StringUtils.hasText(handle)) {
				predicates.add(cb.equal(root.get("handle"), handle));
			}

			if (StringUtils.hasText(name)) {
				predicates.add(cb.like(root.get("name"), "%" + name + "%"));
			}

			return cb.and(predicates.toArray(new Predicate[0]));
		}, pageable);

		var records = p.stream().map(entity -> BeanUtil.copyProperties(entity, HandleDomainEntity.class)).toList();

		return new PageResponse<>(records, p.getTotalElements(), (long) size, (long) page, (long) p.getTotalPages());
	}

	@Override
	public HandleDomainEntity findByHandle(String handle) {
		HandleEntity entity = handleJpaRepository.findByHandle(handle);
		if (entity == null) {
			return new HandleDomainEntity();
		}

		HandleDomainEntity handleDomainEntity = BeanUtil.copyProperties(entity, HandleDomainEntity.class);
		List<HandleItemEntity> items = handleItemJpaRepository.findByHandleId(handleDomainEntity.getId());
		if (items != null && !items.isEmpty()) {
			handleDomainEntity.setHandleItems(BeanUtil.copyToList(items, HandleItemDomainEntity.class));
		}
		return handleDomainEntity;
	}
}
