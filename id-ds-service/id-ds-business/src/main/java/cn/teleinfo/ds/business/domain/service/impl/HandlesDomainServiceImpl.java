package cn.teleinfo.ds.business.domain.service.impl;

import cn.teleinfo.ds.business.domain.model.aggregate.Handle;
import cn.teleinfo.ds.business.domain.model.entity.HandleDomainEntity;
import cn.teleinfo.ds.business.domain.repository.HandlesRepository;
import cn.teleinfo.ds.business.domain.service.HandlesDomainService;
import com.pig4cloud.pig.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@AllArgsConstructor
@Service
public class HandlesDomainServiceImpl implements HandlesDomainService {

	private final HandlesRepository handlesRepository;

	/**
	 * 对象标识列表
	 *
	 * @param handle 聚合根
	 */
	@Override
	public PageResponse<Handle> listHandles(Handle handle, Integer page, Integer size) {
		HandleDomainEntity handleDomainEntity = handle.getHandleDomainEntity();

		PageResponse<HandleDomainEntity> pageResponse = handlesRepository.listHandles(handleDomainEntity.getAppHandleCode(),
				handleDomainEntity.getHandle(), handleDomainEntity.getName(), page, size);

		var records = pageResponse.getRecords().stream()
				.map(Handle::new)
				.toList();

		return new PageResponse<>(records, pageResponse.getTotal(), pageResponse.getSize(), pageResponse.getCurrent(), pageResponse.getPages());
	}

	/**
	 * 查询对象标识
	 *
	 * @param handle 对象标识
	 */
	@Override
	public HandleDomainEntity findByHandle(String handle) {
		return handlesRepository.findByHandle(handle);
	}
}
