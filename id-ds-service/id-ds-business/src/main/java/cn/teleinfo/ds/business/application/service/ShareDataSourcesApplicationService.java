package cn.teleinfo.ds.business.application.service;

import cn.teleinfo.ds.business.application.command.CreateShareDataSourcesCommand;
import cn.teleinfo.ds.business.application.query.ListShareDataSourcesQuery;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareSourceDTO;
import com.pig4cloud.pig.common.core.util.PageResponse;

public interface ShareDataSourcesApplicationService {

	/**
	 * 创建共享数据源
	 *
	 * @param command 共享数据源信息
	 */
	void createShareDataSources(CreateShareDataSourcesCommand command);

	/**
	 * 查询共享源列表
	 */
	PageResponse<ShareSourceDTO>  listShareDataSources(ListShareDataSourcesQuery query);
}
