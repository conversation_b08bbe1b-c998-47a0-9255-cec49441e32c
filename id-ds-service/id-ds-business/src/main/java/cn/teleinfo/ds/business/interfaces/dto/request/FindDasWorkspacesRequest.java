package cn.teleinfo.ds.business.interfaces.dto.request;

import com.pig4cloud.pig.common.core.util.PageRequest;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

// 获取工作空间列表
@Getter
@Setter
public class FindDasWorkspacesRequest extends PageRequest {

	/**
	 * 平台类型
	 * t_connection platform_type
	 */
	@NotNull(message = "平台类型 不能为空")
	private Integer platformType;

	/**
	 * 资源空间
	 */
	@NotNull(message = "资源空间 不能为空")
	private String projectId;

	/**
	 * 实例id
	 */
	@NotNull(message = "实例 不能为空")
	private String instanceId;

}
