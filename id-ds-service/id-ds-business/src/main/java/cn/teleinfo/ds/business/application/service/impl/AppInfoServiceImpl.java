package cn.teleinfo.ds.business.application.service.impl;

import cn.teleinfo.ds.business.application.query.ListAppInfoQuery;
import cn.teleinfo.ds.business.application.service.AppInfoService;
import cn.teleinfo.ds.business.domain.service.AppInfoDomainService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.HandleSignAppInfoDTO;
import com.pig4cloud.pig.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class AppInfoServiceImpl implements AppInfoService {

	private final AppInfoDomainService appInfoDomainService;

	@Override
	public PageResponse<HandleSignAppInfoDTO> listHandleSignAppInfo(ListAppInfoQuery query) {
		return appInfoDomainService.listHandleSignAppInfo(query);
	}

	@Override
	public HandleSignAppInfoDTO queryHandleSignAppInfoDetail(String id) {
		return appInfoDomainService.queryHandleSignAppInfoDetail(id);
	}

}
