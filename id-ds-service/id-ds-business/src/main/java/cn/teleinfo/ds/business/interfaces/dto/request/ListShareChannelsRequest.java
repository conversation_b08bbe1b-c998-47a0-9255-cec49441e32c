package cn.teleinfo.ds.business.interfaces.dto.request;

import com.pig4cloud.pig.common.core.util.PageRequest;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class ListShareChannelsRequest extends PageRequest {

	/**
	 * 通道名
	 */
	private String channelName;

	/**
	 * 所属标识
	 */
	private String handle;

	/**
	 * 探测状态
	 */
	private String status;

	/**
	 * 开始时间
	 */
	private LocalDateTime startTime;

	/**
	 * 结束时间
	 */
	private LocalDateTime endTime;

	/**
	 * 应用编码
	 */
	private String appHandle;

}
