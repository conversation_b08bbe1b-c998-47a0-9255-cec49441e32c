package cn.teleinfo.ds.business.interfaces.rest;

import cn.teleinfo.ds.business.application.service.ShareTaskAuthApplicationService;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskApplicationDetails;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareTaskAuthView;
import cn.teleinfo.ds.business.interfaces.assembler.ShareTaskApplicationsAssemble;
import cn.teleinfo.ds.business.interfaces.assembler.ShareTaskAuthAssembler;
import cn.teleinfo.ds.business.interfaces.dto.request.ListShareTaskAuthsRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.ShareTaskApplicationDetailResponse;
import com.pig4cloud.pig.common.core.util.PageResponse;
import com.pig4cloud.pig.common.core.util.R;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/share-task-auths")
@AllArgsConstructor
public class ShareTaskAuthController {

	private final ShareTaskAuthAssembler assembler;

	private final ShareTaskAuthApplicationService service;

	private final ShareTaskApplicationsAssemble applicationsAssemble;

	/**
	 *共享任务授权列表
	 * @param request 分页请求
	 * @return 分页响应
	 */
	@GetMapping
	public R<PageResponse<ShareTaskAuthView> > listShareTaskAuths(ListShareTaskAuthsRequest request) {
		var query = assembler.toQuery(request);
		return R.ok(service.listShareTaskAuths(query, request));
	}


	/**
	 * 共享任务授权详情
	 * @param shareTaskAuthId 共享任务授权ID
	 * @return 共享任务授权详情
	 */
	@GetMapping("/{shareTaskAuthId}")
	public R<ShareTaskApplicationDetailResponse> getShareTaskApplicationDetail(@PathVariable Long shareTaskAuthId) {
		ShareTaskApplicationDetails detail = service.getShareTaskAuthDetail(shareTaskAuthId);
		return R.ok(applicationsAssemble.toDetailResponse(detail));
	}

}
