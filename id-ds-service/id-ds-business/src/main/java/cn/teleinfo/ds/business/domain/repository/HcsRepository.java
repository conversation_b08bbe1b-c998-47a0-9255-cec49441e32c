package cn.teleinfo.ds.business.domain.repository;

import com.huaweicloud.sdk.cdm.v1.model.Clusters;
import com.huaweicloud.sdk.cdm.v1.model.Job;
import com.huaweicloud.sdk.cdm.v1.model.Links;
import com.huaweicloud.sdk.dataartsstudio.v1.model.*;
import com.huaweicloud.sdk.dgc.v1.model.ScriptInfo;
import com.huaweicloud.sdk.dgc.v1.model.ShowScriptResponse;
import com.huaweicloud.sdk.iam.v3.model.AuthProjectResult;

import java.util.List;
import java.util.Map;

public interface HcsRepository {

	/**
	 * 数据集成-规范层连接名称
	 */
	List<Links> findCdmConnections(String ak, String sk, String projectId, List<String> endpoints, String clusterId);

	/**
	 * 数据开发-规范层连接名称
	 */
	List<ApigDataSourceView>  findDayuConnections(String ak, String sk, String projectId,
												  List<String> endpoints, String workspace, Integer offset, Integer limit);

	/**
	 * 数据开发-规范层数据库名称
	 */
	List<DatabasesList> findDayuConnectionsDatabases(String ak, String sk, String projectId,
													 List<String> endpoints, String workspace, String connectionId, Integer offset, Integer limit);

	/**
	 * 获取资源空间列表
	 */
	List<AuthProjectResult> findProjects(String ak, String sk, List<String> endpoints);

	/**
	 * 获取数据治理中心实例列表
	 */
	List<ApigCommodityOrder> findDasInstances(String ak, String sk, String projectId,
											  List<String> endpoints, Integer offset, Integer limit);

	/**
	 * 获取工作空间列表
	 */
	List<Workspacebody> findDasWorkspaces(String ak, String sk, String projectId, String instanceId,
										  List<String> endpoints, Integer offset, Integer limit) ;

	/**
	 * 获取CDM集群名称列表
	 */
	List<Clusters> findCdmClusters(String ak, String sk, String projectId, List<String> endpoints);

	/**
	 * 获取作业列表
	 */
	List<Job> findJobs(String ak, String sk, String projectId, List<String> endpoints, String clusterId);

	/**
	 * 创建脚本
	 */
	void createScript(String ak, String sk, String projectId, List<String> endpoints, String scriptName, String scriptContent, String workspace,String dataBaseName,String connectionName);

	/**
	 * 查询脚本
	 */
	ScriptInfo findScript(String ak, String sk, String projectId, List<String> endpoints, String workspace, String scriptName);

	/**
	 * 删除脚本
	 */
	void deleteScript(String ak, String sk, String projectId, List<String> endpoints, String scriptName, String workspace);

	/**
	 * 执行脚本
	 */
	String executeScript(String ak, String sk, String projectId, List<String> endpoints, String scriptName, String workspace);

	/** 
	 * 查询脚本实例执行结果
	 */
	Map<String, String> listScriptResults(String ak, String sk, String projectId, List<String> endpoints, String scriptName, String workspace, String instanceId);
	/**
	 * 获取数据源中的表
	 */
	List<TablesList> listTables(String ak, String sk, String projectId, List<String> endpoints, String connectionId, String databaseName, String tableName);

	/**
	 * 获取表的字段
	 */
	List<ColumnsList> listTableColumns(String ak, String sk, String projectId, List<String> endpoints, String connectionId, String workSpace, String tableId);

}
