package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.domain.model.entity.ShareDataSourcesDomainEntity;
import cn.teleinfo.ds.business.domain.repository.ShareDataSourcesRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareSourceDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareSourceEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.ShareSourceJpaRepository;
import com.pig4cloud.pig.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Component
@AllArgsConstructor
public class ShareDataSourcesRepositoryImpl implements ShareDataSourcesRepository {

	private final ShareSourceJpaRepository repository;

	/**
	 * 创建共享数据源
	 *
	 * @param entity 共享数据源信息
	 */
	@Override
	@Transactional(rollbackFor = RuntimeException.class)
	public void createShareDataSources(ShareSourceEntity entity) {
		repository.save(entity);
	}

	/**
	 * 查询共享源列表
	 */
	@Override
	public PageResponse<ShareSourceDTO> listShareDataSources(ShareDataSourcesDomainEntity entity, LocalDateTime start, LocalDateTime end, Integer page, Integer size) {
		int pageIndex = (page > 0) ? page - 1 : 0;
		Pageable pageable = PageRequest.of(pageIndex, size);
		var results = repository.listShareDataSources(entity.getAppHandleCode(), entity.getAppName(), entity.getPlatformType(), entity.getConnState(), start, end, pageable);

		List<ShareSourceDTO> records = results.getContent().stream().map(
				v ->{
					ShareSourceDTO shareSource = new ShareSourceDTO();
					shareSource.setId(v.getId());
					shareSource.setPlatformType(v.getPlatformType());
					shareSource.setConnState(v.getConnState());
					shareSource.setAppHandleCode(v.getAppHandleCode());
					shareSource.setAppName(v.getAppName());
					shareSource.setUpdateBy(v.getUpdateBy());
					shareSource.setCreateTime(v.getCreateTime());
					shareSource.setUpdateTime(v.getUpdateTime());
					shareSource.setUpdateByName(v.getUpdateByName());
					return shareSource;
				}).toList();
		return new PageResponse<>(records, results.getTotalElements(), (long) size, (long) page, (long) results.getTotalPages());
	}

	@Override
	public ShareDataSourcesDomainEntity findByAppHandleCode(String appHandleCode) {
		ShareSourceEntity entity = repository.findFirstByAppHandleCode(appHandleCode);
		return entity != null ? BeanUtil.copyProperties(entity, ShareDataSourcesDomainEntity.class) : null;
	}


}
