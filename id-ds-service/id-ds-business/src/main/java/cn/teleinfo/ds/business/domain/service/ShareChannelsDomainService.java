package cn.teleinfo.ds.business.domain.service;

import cn.teleinfo.ds.business.application.command.CreateShareChannelVersionCommand;
import cn.teleinfo.ds.business.application.command.UpdateShareChannelsApplicationsCommand;
import cn.teleinfo.ds.business.application.query.ListShareChannelsQuery;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareChannelsApplicationsDetail;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareChannelsVersion;
import cn.teleinfo.ds.business.domain.model.entity.ShareChannel;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsDetailsDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsApplicationsView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsVersionSqlView;
import com.pig4cloud.pig.common.core.util.PageResponse;

import java.util.List;

public interface ShareChannelsDomainService {

	PageResponse<ShareChannelsDTO> listShareChannels(ListShareChannelsQuery query);

	ShareChannelsDetailsDTO queryShareChannelsDetails(String id);

	List<ShareChannelsDetailsDTO> listShareChannelsVersion(String shareChannelId);

	ShareChannelsVersionSqlView queryVersionSql(String shareChannelId, String version);

	List<ShareChannelsVersion> shareChannelsVersion(String shareChannelId);

	PageResponse<ShareChannelsApplicationsView> listShareChannelsApplications(ListShareChannelsQuery query);

	ShareChannelsApplicationsDetail queryShareChannelsApplicationDetails(String applicationId);

	void createShareChannels();

	void createShareChannel(Long shareChannelId);

	void reviewShareChannels(String applicationId, UpdateShareChannelsApplicationsCommand command);

	List<String> queryChannelVersions(String shareChannelId);

	void shareChannelVersionSave(CreateShareChannelVersionCommand command);

	void shareChannelVersionChange(String shareChannelId, String version);

	ShareChannel findEnableChannel(Long id);
	void detectShareChannels();
}
