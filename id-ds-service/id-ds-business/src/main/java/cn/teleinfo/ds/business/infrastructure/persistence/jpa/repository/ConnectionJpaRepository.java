package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ConnectionEntity;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ConnectionJpaRepository extends BaseRepository<ConnectionEntity, Long> {

	Optional<ConnectionEntity> findByPlatformType(Integer platformType);


}
