package cn.teleinfo.ds.business.interfaces.rest;

import cn.teleinfo.ds.business.application.query.ListShareChannelsQuery;
import cn.teleinfo.ds.business.application.service.ShareChannelsAppService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsVersionSqlView;
import cn.teleinfo.ds.business.interfaces.assembler.ShareChannelsAssembler;
import cn.teleinfo.ds.business.interfaces.dto.request.ListShareChannelsRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.ShareChannelRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.ShareChannelSaveRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.ShareChannelsDetailsResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.ShareChannelsResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.ShareChannelsVersionResponse;
import com.pig4cloud.pig.common.core.util.PageResponse;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.common.security.annotation.Inner;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 共享通道
 */
@RestController
@RequestMapping("/share-channels")
@AllArgsConstructor
public class ShareChannelsController {

	private final ShareChannelsAppService shareChannelsAppService;

	private final ShareChannelsAssembler shareChannelsAssembler;

	/**
	 * 共享通道列表查询
	 *
	 * @param request 过滤条件
	 */
	@GetMapping
	public R<PageResponse<ShareChannelsResponse>> listShareChannels(@Valid ListShareChannelsRequest request) {
		if (request.getPage() == null || request.getSize() == null) {
			return R.failed("分页参数必填!");
		}
		ListShareChannelsQuery listShareChannelsQuery = shareChannelsAssembler.toListShareChannelsQuery(request);
		var shareChannelsDTOPageList = shareChannelsAppService.listShareChannels(listShareChannelsQuery);
		return R.ok(shareChannelsAssembler.toShareChannelsResponse(shareChannelsDTOPageList));
	}

	/**
	 * 共享通道详情
	 *
	 * @param id 共享通道id
	 */
	@GetMapping("/{id}")
	public R<ShareChannelsDetailsResponse> queryShareChannelsDetails(@PathVariable("id") String id) {
		if (StringUtils.isBlank(id)) {
			return R.failed("共享通道id必填!");
		}
		var shareChannelsDetailsDTOList = shareChannelsAppService.queryShareChannelsDetails(id);
		return R.ok(shareChannelsAssembler.toShareChannelsDetailsResponse(shareChannelsDetailsDTOList));
	}

	/**
	 * 共享通道版本清单
	 *
	 * @param shareChannelId 共享通道id
	 */
	@GetMapping("/versions/{shareChannelId}")
	public R<List<ShareChannelsDetailsResponse>> listShareChannelsVersion(
			@PathVariable("shareChannelId") String shareChannelId) {
		if (StringUtils.isBlank(shareChannelId)) {
			return R.failed("共享通道id必填!");
		}
		var shareChannelsDetailsDTOList = shareChannelsAppService.listShareChannelsVersion(shareChannelId);
		return R.ok(shareChannelsAssembler.toShareChannelsDetailsResponse(shareChannelsDetailsDTOList));
	}

	/**
	 * 共享通道维护信息
	 *
	 * @param shareChannelId 共享通道id
	 * @param version 版本
	 */
	@GetMapping("/{shareChannelId}/{version}")
	public R<ShareChannelsVersionSqlView> queryVersionSql(@PathVariable("shareChannelId") String shareChannelId,
			@PathVariable("version") String version) {
		if (StringUtils.isBlank(shareChannelId)) {
			return R.failed("共享通道id必填!");
		}
		if (StringUtils.isBlank(version)) {
			return R.failed("版本必填!");
		}

		return R.ok(shareChannelsAppService.queryVersionSql(shareChannelId, version));
	}

	/**
	 * 共享通道维护版本信息
	 *
	 * @param shareChannelId 共享通道id
	 */
	@GetMapping("/{shareChannelId}/versions")
	public R<List<String>> queryChannelVersions(@PathVariable("shareChannelId") String shareChannelId) {
		if (StringUtils.isBlank(shareChannelId)) {
			return R.failed("共享通道id必填!");
		}

		return R.ok(shareChannelsAppService.queryChannelVersions(shareChannelId));
	}

	/**
	 * 共享通道版本
	 *
	 * @param shareChannelId 共享通道id
	 */
	@GetMapping("/shareChannelsVersion/{shareChannelId}")
	public R<List<ShareChannelsVersionResponse>> shareChannelsVersion(
			@PathVariable("shareChannelId") String shareChannelId) {
		if (StringUtils.isBlank(shareChannelId)) {
			return R.failed("共享通道id必填!");
		}
		var shareChannelsDetailsDTOList = shareChannelsAppService.shareChannelsVersion(shareChannelId);
		return R.ok(shareChannelsAssembler.toShareChannelsVersionResponse(shareChannelsDetailsDTOList));
	}

	/**
	 * 共享通道定时生成
	 */
	@Inner
	@GetMapping("/auto-create")
	public R createShareChannels() {
		shareChannelsAppService.createShareChannels();
		return R.ok();
	}

	/**
	 * 共享通道手动生成
	 */
	@PostMapping("/generate")
	public R createShareChannel(@RequestBody ShareChannelRequest request) {
		shareChannelsAppService.createShareChannel(request.getShareChannelId());
		return R.ok();
	}

	/**
	 * 共享通道维护保存
	 */
	@PostMapping
	public R shareChannelVersionSave(@RequestBody ShareChannelSaveRequest request) {
		var command = shareChannelsAssembler.toCreateShareChannelVersionCommand(request);
		shareChannelsAppService.shareChannelVersionSave(command);
		return R.ok();
	}

	/**
	 * 共享通道版本切换
	 *
	 * @param shareChannelId 共享通道id
	 * @param version 版本
	 */
	@PutMapping("/{shareChannelId}/{version}/enable")
	public R shareChannelVersionChange(@PathVariable("shareChannelId") String shareChannelId,
			@PathVariable("version") String version) {
		shareChannelsAppService.shareChannelVersionChange(shareChannelId, version);
		return R.ok();
	}


	/**
	 * 共享通道探测
	 */
	@Inner
	@GetMapping("/detect")
	public R detectShareChannels() {
		shareChannelsAppService.detectShareChannels();
		return R.ok();
	}

}
