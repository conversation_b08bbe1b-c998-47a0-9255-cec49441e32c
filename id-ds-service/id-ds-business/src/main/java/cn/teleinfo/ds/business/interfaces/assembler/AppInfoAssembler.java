package cn.teleinfo.ds.business.interfaces.assembler;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.application.query.ListAppInfoQuery;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.HandleSignAppInfoDTO;
import cn.teleinfo.ds.business.interfaces.dto.request.ListHandleSignRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleSignAppInfoResponse;
import com.pig4cloud.pig.common.core.util.PageResponse;
import org.springframework.stereotype.Component;

@Component
public class AppInfoAssembler {

	public ListAppInfoQuery toListAppInfoQuery(ListHandleSignRequest request) {
		return BeanUtil.copyProperties(request, ListAppInfoQuery.class);
	}

	public PageResponse<HandleSignAppInfoResponse> toHandleSignAppInfoResponse(
			PageResponse<HandleSignAppInfoDTO> handleSignAppInfoDTOPageResponse) {
		var records = handleSignAppInfoDTOPageResponse.getRecords()
			.stream()
			.map(t -> BeanUtil.copyProperties(t, HandleSignAppInfoResponse.class))
			.toList();
		return new PageResponse<>(records, handleSignAppInfoDTOPageResponse.getTotal(),
				handleSignAppInfoDTOPageResponse.getSize(), handleSignAppInfoDTOPageResponse.getCurrent(),
				handleSignAppInfoDTOPageResponse.getPages());
	}

	public HandleSignAppInfoResponse toHandleSignAppInfoResponse(HandleSignAppInfoDTO handleSignAppInfoDTO) {
		return BeanUtil.copyProperties(handleSignAppInfoDTO, HandleSignAppInfoResponse.class);
	}

}
