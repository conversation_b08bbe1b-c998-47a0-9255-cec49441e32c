package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.application.query.ListAppInfoQuery;
import cn.teleinfo.ds.business.domain.model.entity.AppInfoDomainEntity;
import cn.teleinfo.ds.business.domain.repository.AppInfoRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.HandleSignAppInfoDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.AppInfoEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.AppInfoJpaRepository;
import com.pig4cloud.pig.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AllArgsConstructor
public class AppInfoRepositoryImpl implements AppInfoRepository {

	private final AppInfoJpaRepository appInfoJpaRepository;

	/**
	 * 查询企业前缀下的应用
	 * @param entPrefix 企业前缀
	 * @return
	 */
	@Override
	public List<AppInfoDomainEntity> findAllByEntPrefix(String entPrefix) {
		List<AppInfoEntity> all = appInfoJpaRepository.findByEntPrefix(entPrefix);
		return BeanUtil.copyToList(all, AppInfoDomainEntity.class);
	}

	/**
	 * 应用信息列表查询
	 * @param query 过滤条件
	 */
	@Override
	public PageResponse<HandleSignAppInfoDTO> listHandleSignAppInfo(ListAppInfoQuery query, Integer page,
																	Integer size) {
		int pageIndex = (page > 0) ? page - 1 : 0;
		Pageable pageable = PageRequest.of(pageIndex, size);
		var results = appInfoJpaRepository.listHandleSignAppInfo(query.getAppName(), query.getHandleCode(),
				pageable);

		return new PageResponse<>(results.toList(), results.getTotalElements(), (long) size, (long) page,
				(long) results.getTotalPages());
	}

	/**
	 * 应用信息详情
	 * @param id id
	 */
	@Override
	public HandleSignAppInfoDTO queryHandleSignAppInfoDetail(String id) {
		return appInfoJpaRepository.queryHandleSignAppInfoDetail(id);
	}

	@Override
	public AppInfoDomainEntity findByHandleCode(String handleCode) {
		AppInfoEntity appInfo = appInfoJpaRepository.findByHandleCode(handleCode);
		return BeanUtil.copyProperties(appInfo,AppInfoDomainEntity.class);
	}
}
