package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareTaskAuthDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareTaskAuthEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareTaskAuthView;
import feign.Param;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ShareTaskAuthJpaRepository extends BaseRepository<ShareTaskAuthEntity, Long> {

	@Query(nativeQuery = true,
			value = """
					SELECT
					    s.id,
					    s.task_no AS taskNo,
					    s.task_status AS authStatus,
					    u.name AS auditUserName,
					     DATE_FORMAT(s.update_time, '%Y-%m-%d %H:%i:%s') AS updateTime,
					     s.audit_remark AS auditRemark
					FROM t_share_task_auth s,  sys_user u
					WHERE s.share_task_applications_id = :applicationId
					AND s.update_by = u.user_id
					AND s.is_deleted = 0
					"""
	)
	List<ShareTaskAuthDTO> findShareTaskAuthsByApplicationId(@Param("applicationId")Long applicationId);


	@Query(nativeQuery = true,
			value = """
					SELECT
					    s.id,
					    s.task_no AS taskNo,
					    s.task_status AS authStatus,
					    u.name AS auditUserName,
					     s.update_time AS updateTime,
					     s.audit_remark AS auditRemark
					FROM t_share_task_auth s
					LEFT JOIN sys_user u ON s.update_by = u.user_id
					WHERE IF(:taskNo != '' AND :taskNo is not null, s.task_name like CONCAT('%',:taskNo,'%'), 1=1 )
					and IF(:authStatus != '' AND :authStatus is not null, s.auth_status = :authStatus, 1=1 )
					and IF(:startTime != '' AND :startTime is not null, s.update_time >= :startTime, 1=1 )
					and IF(:endTime != '' AND :endTime is not null, s.update_time <= :endTime, 1=1 )
					and s.is_deleted = 0
					""",
			countQuery = """
 					SELECT COUNT(*)
 					FROM t_share_task_auth s
 					LEFT JOIN sys_user u ON s.update_by = u.user_id
					WHERE IF(:taskNo != '' AND :taskNo is not null, s.task_name like CONCAT('%',:taskNo,'%'), 1=1 )
					and IF(:authStatus != '' AND :authStatus is not null, s.auth_status = :authStatus, 1=1 )
					and IF(:startTime != '' AND :startTime is not null, s.update_time >= :startTime, 1=1 )
					and IF(:endTime != '' AND :endTime is not null, s.update_time <= :endTime, 1=1 )
					and s.is_deleted = 0
					"""
	)
	Page<ShareTaskAuthView> findShareTaskAuths(@Param("taskNo") String taskNo, @Param("authStatus") Integer authStatus, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, Pageable pageable);

	ShareTaskAuthEntity findFirstByShareTaskApplicationsIdAndAuthStatus(Long applicationId, Integer authStatus);
}