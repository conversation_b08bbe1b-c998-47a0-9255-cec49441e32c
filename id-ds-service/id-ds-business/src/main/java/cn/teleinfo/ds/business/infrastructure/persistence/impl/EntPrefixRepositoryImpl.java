package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.domain.model.entity.EntPrefixDomainEntity;
import cn.teleinfo.ds.business.domain.repository.EntPrefixRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.EntPrefixEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.EntPrefixJpaRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AllArgsConstructor
public class EntPrefixRepositoryImpl implements EntPrefixRepository {
	private final EntPrefixJpaRepository entPrefixJpaRepository;
	/**
	 * 查询省级下的企业前缀
	 *
	 * @param provincePrefix 省级前缀
	 * @return
	 */
	@Override
	public List<EntPrefixDomainEntity> findAllByProvincePrefix(String provincePrefix) {
		List<EntPrefixEntity> all = entPrefixJpaRepository.findByProvincePrefix(provincePrefix);
		return BeanUtil.copyToList(all, EntPrefixDomainEntity.class);
	}
}
