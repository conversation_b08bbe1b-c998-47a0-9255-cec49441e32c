package cn.teleinfo.ds.business.interfaces.rest;

import cn.teleinfo.ds.business.application.command.UpdateConnectionSettingCommand;
import cn.teleinfo.ds.business.application.service.SysApplicationConnService;
import cn.teleinfo.ds.business.interfaces.assembler.SysConnectionAssembler;
import cn.teleinfo.ds.business.interfaces.dto.request.UpdateConnectionSettingRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.SysConnectionResponse;
import com.pig4cloud.pig.common.core.util.R;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 系统设置
 * 连接管理
 */
@RestController
@RequestMapping("/sys/connection")
@AllArgsConstructor
public class SysConnectionController {

	private final SysApplicationConnService sysApplicationConnService;
	private final SysConnectionAssembler sysConnectionAssembler;

	/**
	 * 查询系统连接配置
	 *
	 * @param platformType 平台类型
	 * @return 系统连接配置
	 */
	@GetMapping
	public R<SysConnectionResponse> getConnectionSetting(@RequestParam("platformType") Integer platformType) {
		var conn = sysApplicationConnService.getConnectionSetting(platformType);
		return R.ok(sysConnectionAssembler.toResponse(conn));
	}

	/**
	 * 更新系统连接配置
	 *
	 * @param request 系统连接配置信息
	 * @return
	 */
	@PutMapping
	public R updateConnectionSetting(@RequestBody @Valid UpdateConnectionSettingRequest request) {
		UpdateConnectionSettingCommand command = sysConnectionAssembler.toUpdateConnectionSettingCommand(request);
		sysApplicationConnService.updateConnectionSetting(command);
		return R.ok();
	}
}
