package cn.teleinfo.ds.business.domain.service;

import cn.teleinfo.ds.business.application.query.DataChannelsListQuery;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.DataChannelsView;
import com.pig4cloud.pig.common.core.util.PageResponse;

public interface DataChannelsDomainService {

	/**
	 * 查询数据通道列表
	 * @param query 查询条件
	 * @return 分页数据
	 */
	PageResponse<DataChannelsView> listDataChannels(DataChannelsListQuery query);
}
