package cn.teleinfo.ds.business.domain.model.aggregate;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.domain.model.valueobject.ShareChannelAuthValue;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsVersionAuthDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsVersionDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsApplicationsDetailView;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShareChannelsApplicationsDetail {

	/**
	 * id
	 */
	private String id;

	/**
	 * 共享通道id
	 */
	private String shareChannelId;

	/**
	 * 数据通道id
	 */
	private String dataChannelId;

	/**
	 * 共享通道名称
	 */
	private String shareChannelName;

	/**
	 * 所属标识
	 */
	private String handle;

	/**
	 * 数据类型
	 */
	private String dataType;

	/**
	 * 主版本号
	 */
	private Integer mainVersion;

	/**
	 * 次版本号
	 */
	private Integer minorVersion;

	/**
	 * 应用名
	 */
	private String appName;

	/**
	 * 企业名
	 */
	private String entName;

	/**
	 * 探测状态
	 */
	private String status;

	/**
	 * 自动sql
	 */
	private String autoSql;

	/**
	 * 手动sql
	 */
	private String editSql;

	/**
	 * 申请人
	 */
	private String applyUser;

	/**
	 * 申请时间
	 */
	private LocalDateTime updatedTime;

	/**
	 * 变更原因
	 */
	private String changeReason;

	/**
	 * 审核记录
	 */
	private List<ShareChannelAuthValue> items;

	public ShareChannelsApplicationsDetail(ShareChannelsApplicationsDetailView shareChannelsVersionDTO,
										   List<ShareChannelsVersionAuthDTO> shareChannelsVersionAuthDTOList) {
		this.id = shareChannelsVersionDTO.getId().toString();
		this.shareChannelId = shareChannelsVersionDTO.getShareChannelId().toString();
		this.dataChannelId = shareChannelsVersionDTO.getDataChannelId().toString();
		this.shareChannelName = shareChannelsVersionDTO.getShareChannelName();
		this.handle = shareChannelsVersionDTO.getHandle();
		this.dataType = shareChannelsVersionDTO.getDataType().toString();
		this.mainVersion = shareChannelsVersionDTO.getMainVersion();
		this.minorVersion = shareChannelsVersionDTO.getMinorVersion();
		this.appName = shareChannelsVersionDTO.getAppName();
		this.entName = shareChannelsVersionDTO.getEntName();
		this.status = shareChannelsVersionDTO.getStatus().toString();
		this.autoSql = shareChannelsVersionDTO.getAutoSql();
		this.editSql = shareChannelsVersionDTO.getEditSql();
		this.applyUser = shareChannelsVersionDTO.getApplyUser();
		this.updatedTime = shareChannelsVersionDTO.getUpdatedTime();
		this.changeReason = shareChannelsVersionDTO.getChangeReason();
		this.items = BeanUtil.copyToList(shareChannelsVersionAuthDTOList, ShareChannelAuthValue.class);
	}

}
