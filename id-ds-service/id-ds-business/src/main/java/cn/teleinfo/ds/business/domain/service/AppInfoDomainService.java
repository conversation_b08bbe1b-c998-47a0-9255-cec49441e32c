package cn.teleinfo.ds.business.domain.service;

import cn.teleinfo.ds.business.application.query.ListAppInfoQuery;
import cn.teleinfo.ds.business.domain.model.entity.AppInfoDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.HandleSignAppInfoDTO;
import com.pig4cloud.pig.common.core.util.PageResponse;

public interface AppInfoDomainService {

	PageResponse<HandleSignAppInfoDTO> listHandleSignAppInfo(ListAppInfoQuery query);

	HandleSignAppInfoDTO queryHandleSignAppInfoDetail(String id);

	AppInfoDomainEntity findByHandleCode(String appHandleCode);

}
