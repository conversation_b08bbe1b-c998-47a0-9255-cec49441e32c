server:
  port: 2000

spring:
  application:
    name: @artifactId@
  cloud:
    nacos:
      username: nacos
      password: 47Ac5H78@YyNw
      discovery:
        server-addr: ${NACOS_HOST:*************}:${NACOS_PORT:8848}
        namespace: ${NACOS_NAMESPACE:public}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: ${spring.cloud.nacos.discovery.namespace}
  config:
    import:
      - optional:nacos:<EMAIL>@.yml
      - optional:nacos:${spring.application.name}-@profiles.active@.yml
  quartz:
    auto-startup: true
    job-store-type: jdbc # 持久化到数据库
    scheduler-name: dsScheduler
    properties:
      org:
        quartz:
          scheduler:
            instanceName: dsScheduler
            instanceId: AUTO
          jobStore:
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore # 数据库存储
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate # 数据库代理
            tablePrefix: QRTZ_ # 表前缀
            isClustered: true # 集群
            clusterCheckinInterval: 30000 # 集群检查间隔
            useProperties: false
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool # 线程池
            threadCount: 50 # 线程数量
            threadPriority: 5 # 线程优先级
            threadsInheritContextClassLoaderOfInitializingThread: true # 是否继承类加载器
    jdbc:
      initialize-schema: never # 不初始化表结构
