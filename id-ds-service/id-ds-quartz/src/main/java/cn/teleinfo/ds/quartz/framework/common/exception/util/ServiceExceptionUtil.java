package cn.teleinfo.ds.quartz.framework.common.exception.util;


import cn.teleinfo.ds.quartz.framework.common.exception.ErrorCode;
import cn.teleinfo.ds.quartz.framework.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;

/**
 * 异常工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class ServiceExceptionUtil {

    public static ServiceException exception(ErrorCode errorCode) {
        return new ServiceException(errorCode.getCode(), errorCode.getMsg());
    }

    public static ServiceException exception0(Integer code, String message) {
        return new ServiceException(code, message);
    }

} 