package cn.teleinfo.ds.quartz.job;


import cn.teleinfo.ds.quartz.framework.quartz.core.handler.JobHandler;
import cn.teleinfo.ds.upms.api.feign.UserSyncService;
import com.pig4cloud.pig.common.core.util.R;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * 同步数据Job
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SyncUserJob implements JobHandler {

	@Resource
	private UserSyncService userSyncService;


	@Override
	public String execute(String param) {
		R r = userSyncService.sync();
		if (r.getCode() == 200) {
			return "同步数据成功";
		} else {
			return "同步数据失败";
		}
	}

}
