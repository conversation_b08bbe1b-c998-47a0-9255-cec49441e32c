package cn.teleinfo.ds.quartz.framework.common.pojo;


import cn.teleinfo.ds.quartz.framework.common.exception.ErrorCode;
import cn.teleinfo.ds.quartz.framework.common.exception.ServiceException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * 通用返回
 *
 * @param <T> 数据泛型
 */
@Schema(description = "通用返回")
@Data
public class CommonResult<T> implements Serializable {

    /**
     * 错误码
     */
    @Schema(description = "错误码", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer code;
    /**
     * 返回数据
     */
    @Schema(description = "数据", requiredMode = Schema.RequiredMode.REQUIRED)
    private T data;
    /**
     * 错误提示，用户可阅读
     */
    @Schema(description = "错误提示", requiredMode = Schema.RequiredMode.REQUIRED)
    private String msg;

    /**
     * 将传入的 result 对象，转换成另外一个泛型结果的对象
     *
     * @param result 传入的 result 对象
     * @param <T>    返回的泛型
     * @return 新的 CommonResult 对象
     */
    public static <T> CommonResult<T> error(CommonResult<?> result) {
        return error(result.getCode(), result.getMsg());
    }

    public static <T> CommonResult<T> error(Integer code, String message) {
        CommonResult<T> result = new CommonResult<>();
        result.code = code;
        result.msg = message;
        return result;
    }

    public static <T> CommonResult<T> error(ErrorCode errorCode) {
        return error(errorCode.getCode(), errorCode.getMsg());
    }

    public static <T> CommonResult<T> success(T data) {
        CommonResult<T> result = new CommonResult<>();
        result.code = 0;
        result.data = data;
        result.msg = "";
        return result;
    }

    public static boolean isSuccess(Integer code) {
        return Objects.equals(code, 0);
    }

    /**
     * 判断是否有异常。如果有，则抛出 {@link ServiceException} 异常
     */
    public void checkError() throws ServiceException {
        if (isSuccess(code)) {
            return;
        }
        // 业务异常
        throw new ServiceException(code, msg);
    }

    /**
     * 判断是否有异常。如果有，则抛出 {@link ServiceException} 异常
     * 如果没有，则返回 {@link #data} 数据
     */
    public T getCheckedData() {
        checkError();
        return data;
    }

    public static <T> CommonResult<T> success() {
        return success(null);
    }

} 