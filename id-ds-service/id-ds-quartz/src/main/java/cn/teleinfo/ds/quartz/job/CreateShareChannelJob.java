package cn.teleinfo.ds.quartz.job;


import cn.teleinfo.ds.business.api.feign.BusinessService;
import cn.teleinfo.ds.quartz.framework.quartz.core.handler.JobHandler;
import cn.teleinfo.ds.upms.api.feign.UserSyncService;
import com.pig4cloud.pig.common.core.util.R;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * 自动创建共享通道Job
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CreateShareChannelJob implements JobHandler {

	@Resource
	private BusinessService businessService;


	@Override
	public String execute(String param) {
		R r = businessService.autoCreateShareChannel();
		if (r.getCode() == 200) {
			return "创建成功";
		} else {
			return "创建失败";
		}
	}

}
