package cn.teleinfo.ds.quartz.entity;


import cn.teleinfo.ds.quartz.enums.JobStatusEnum;
import cn.teleinfo.ds.quartz.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 定时任务 DO
 *
 * <AUTHOR>
 */
@TableName("t_job")
@KeySequence("infra_job_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class JobDO extends BaseDO {

	/**
	 * 任务编号
	 */
	@TableId
	private Long id;
	/**
	 * 任务名称
	 */
	private String name;
	/**
	 * 任务状态
	 * <p>
	 * 枚举 {@link JobStatusEnum}
	 */
	private Integer status;

	/**
	 * 任务唯一id
	 * 为空时则使用 handlerName 作为id。
	 */
	private String identity;

	/**
	 * 处理器的名字
	 */
	private String handlerName;
	/**
	 * 处理器的参数
	 */
	private String handlerParam;
	/**
	 * CRON 表达式
	 */
	private String cronExpression;

	/**
	 * 重试次数
	 * <p>
	 * 如果不重试，则设置为 0
	 */
	private Integer retryCount;
	/**
	 * 重试间隔，单位：毫秒
	 * <p>
	 * 如果没有间隔，则设置为 0
	 */
	private Integer retryInterval;
	/**
	 * 监控超时时间，单位：毫秒
	 * <p>
	 * 如果不监控超时，则设置为 0
	 */
	private Integer monitorTimeout;

} 