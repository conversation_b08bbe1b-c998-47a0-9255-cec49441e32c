package cn.teleinfo.ds.quartz.feign;


import cn.teleinfo.ds.quartz.vo.JobPageReqVO;
import cn.teleinfo.ds.quartz.vo.JobRespVO;
import cn.teleinfo.ds.quartz.vo.JobSaveReqVO;

import cn.teleinfo.ds.quartz.vo.JobLogPageReqVO;
import cn.teleinfo.ds.quartz.vo.JobLogRespVO;
import cn.teleinfo.ds.quartz.framework.common.pojo.CommonResult;
import cn.teleinfo.ds.quartz.framework.common.pojo.PageResult;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 定时任务 API 接口
 */
@FeignClient(name = "${feign.client.job-api.name:yudao-module-job}")
public interface JobApi {

    /**
     * 创建定时任务
     */
    @PostMapping("/job/job/create")
	CommonResult<Long> createJob(@Valid @RequestBody JobSaveReqVO createReqVO);

    /**
     * 更新定时任务
     */
    @PutMapping("/job/job/update")
    CommonResult<Boolean> updateJob(@Valid @RequestBody JobSaveReqVO updateReqVO);

    /**
     * 更新定时任务的状态
     */
    @PutMapping("/job/job/update-status")
    CommonResult<Boolean> updateJobStatus(@RequestParam("id") Long id, @RequestParam("status") Integer status);

    /**
     * 触发定时任务
     */
    @PutMapping("/job/job/trigger")
    CommonResult<Boolean> triggerJob(@RequestParam("id") Long id);

    /**
     * 删除定时任务
     */
    @DeleteMapping("/job/job/delete")
    CommonResult<Boolean> deleteJob(@RequestParam("id") Long id);

    /**
     * 获得定时任务
     */
    @GetMapping("/job/job/get")
    CommonResult<JobRespVO> getJob(@RequestParam("id") Long id);

    /**
     * 获得定时任务分页
     */
    @GetMapping("/job/job/page")
    CommonResult<PageResult<JobRespVO>> getJobPage(@Valid JobPageReqVO pageVO);

    /**
     * 获得定时任务的下 n 次执行时间
     */
    @GetMapping("/job/job/get_next_times")
    CommonResult<List<LocalDateTime>> getJobNextTimes(@RequestParam("id") Long id, 
                                                     @RequestParam(value = "count", required = false, defaultValue = "5") Integer count);


	/**
	 * 获得定时任务日志
	 */
	@GetMapping("/job/job-log/get")
	CommonResult<JobLogRespVO> getJobLog(@RequestParam("id") Long id);

	/**
	 * 获得定时任务日志分页
	 */
	@GetMapping("/job/job-log/page")
	CommonResult<PageResult<JobLogRespVO>> getJobLogPage(@Valid JobLogPageReqVO pageVO);

} 