package cn.teleinfo.ds.quartz.service;


import cn.teleinfo.ds.quartz.vo.JobPageReqVO;
import cn.teleinfo.ds.quartz.vo.JobSaveReqVO;
import cn.teleinfo.ds.quartz.entity.JobDO;
import cn.teleinfo.ds.quartz.framework.common.pojo.PageResult;
import jakarta.validation.Valid;
import org.quartz.SchedulerException;

import java.util.Collection;
import java.util.List;

/**
 * 定时任务 Service 接口
 *
 * <AUTHOR>
 */
public interface JobService {

    /**
     * 创建定时任务
     *
     * @param createReqVO 创建信息
     * @return 编号
     * @throws SchedulerException 当 Quartz 异常时
     */
    Long createJob(@Valid JobSaveReqVO createReqVO) throws SchedulerException;

    /**
     * 更新定时任务
     *
     * @param updateReqVO 更新信息
     * @throws SchedulerException 当 Quartz 异常时
     */
    void updateJob(@Valid JobSaveReqVO updateReqVO) throws SchedulerException;

    /**
     * 更新定时任务的状态
     *
     * @param id 任务编号
     * @param status 状态
     * @throws SchedulerException 当 Quartz 异常时
     */
    void updateJobStatus(Long id, Integer status) throws SchedulerException;

    /**
     * 触发定时任务
     *
     * @param id 任务编号
     * @throws SchedulerException 当 Quartz 异常时
     */
    void triggerJob(Long id) throws SchedulerException;

    /**
     * 删除定时任务
     *
     * @param id 编号
     * @throws SchedulerException 当 Quartz 异常时
     */
    void deleteJob(Long id) throws SchedulerException;

    /**
     * 获得定时任务
     *
     * @param id 编号
     * @return 定时任务
     */
    JobDO getJob(Long id);

    /**
     * 获得定时任务列表
     *
     * @param ids 编号
     * @return 定时任务列表
     */
    List<JobDO> getJobList(Collection<Long> ids);

    /**
     * 获得定时任务分页
     *
     * @param pageReqVO 分页查询
     * @return 定时任务分页
     */
    PageResult<JobDO> getJobPage(JobPageReqVO pageReqVO);

    /**
     * 同步定时任务到 Quartz 中
     *
     * @throws SchedulerException 当 Quartz 异常时
     */
    void syncJob() throws SchedulerException;

    /**
     * 获取指定状态的任务列表
     *
     * @param status 状态
     * @return 定时任务列表
     */
    List<JobDO> getJobsByStatus(Integer status);

} 