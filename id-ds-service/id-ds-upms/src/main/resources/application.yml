server:
  port: 4000

spring:
  application:
    name: @artifactId@
  cloud:
    nacos:
      username: nacos
      password: 47Ac5H78@YyNw
      discovery:
        server-addr: ${NACOS_HOST:*************}:${NACOS_PORT:8848}
        namespace: ${NACOS_NAMESPACE:public}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: ${spring.cloud.nacos.discovery.namespace}
  config:
    import:
      - nacos:<EMAIL>@.yml
      - nacos:${spring.application.name}-@profiles.active@.yml


