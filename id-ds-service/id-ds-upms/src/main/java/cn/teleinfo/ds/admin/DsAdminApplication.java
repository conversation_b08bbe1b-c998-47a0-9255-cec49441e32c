package cn.teleinfo.ds.admin;

import com.pig4cloud.pig.common.feign.annotation.EnablePigFeignClients;
import com.pig4cloud.pig.common.security.annotation.EnablePigResourceServer;
import cn.teleinfo.ds.common.swagger.annotation.EnableDoc;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * <AUTHOR>
 * @date 2018年06月21日
 * <p>
 * 用户统一管理系统
 */
@EnableDoc(value = "admin")
@EnablePigFeignClients
@EnablePigResourceServer
@EnableDiscoveryClient
@SpringBootApplication
public class DsAdminApplication {

	public static void main(String[] args) {
		SpringApplication.run(DsAdminApplication.class, args);
	}

}
