package cn.teleinfo.ds.admin.util;

import cn.teleinfo.ds.admin.dto.SyncUserDTO;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
@AllArgsConstructor
public class SyncUserUtil {

	private final OkHttpClient httpClient;
	private final ObjectMapper objectMapper;
	private final IpProperties ipProperties;


	public List<SyncUserDTO> integratedUsers() {
		List<SyncUserDTO> users = new ArrayList<>();
		String url = ipProperties.getIp().concat(ipProperties.getSyncUserUrl());
		Request request = new Request.Builder()
				.url(url)
				.get()
				.build();
		Response response;
		try {
			response = httpClient.newCall(request).execute();
			boolean successful = response.isSuccessful();
			if (!successful) {
				log.error("用户同步接口连接错误, code:{}, body:{}", response.code(),
						response.body());
				return users;
			}
			String result = response.body().string();
			JsonNode jsonNode = objectMapper.readTree(result);
			int code = jsonNode.get("code").asInt();
			if (code != 200) {
				String message = jsonNode.get("message").asText();
				log.error("同步接口错误, code:{}, body:{}", code, message);
				return users;
			}
			JsonNode data = jsonNode.get("data");
			users = objectMapper.convertValue(data,
					TypeFactory.defaultInstance().constructCollectionType(List.class, SyncUserDTO.class));


		} catch (IOException e) {
			log.error("同步用户数据异常", e);
		}
		return users;
	}
}
