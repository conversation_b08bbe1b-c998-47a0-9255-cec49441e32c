package cn.teleinfo.ds.admin.mapper;

import cn.teleinfo.ds.upms.api.vo.RoleCommonVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.teleinfo.ds.upms.api.entity.SysRole;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-29
 */
@Mapper
public interface SysRoleMapper extends BaseMapper<SysRole> {

	/**
	 * 通过用户ID，查询角色信息
	 * @param userId
	 * @return
	 */
	List<SysRole> listRolesByUserId(Long userId);

	List<RoleCommonVO> getRoleListByUserId(Long userId);

}
